---

install_awscli: false
install_cloudwatch_agent: true
install_custom_metrics: false
install_codedeploy: true
install_cfn_bootstrap: false
ec2_assign_elastic_ip: false
logs: ''
aws_env: true
autoscaling: false

instanceidfile: /var/tmp/aws-mon/instance-id
additional_append_dimensions: []
additional_aggregation_dimensions: []
custom_metrics:
  "cpu": { "enabled": true, "interval": 60, "template": "cpu_custom_metrics.j2" }
  "disk": { "enabled": true, "interval": 60, "template": "disk_custom_metrics.j2"  }
  "diskio": { "enabled": true, "interval": 60, "template": "diskio_custom_metrics.j2"  }
  "mem": { "enabled": true, "interval": 60, "template": "mem_custom_metrics.j2"  }
  "net": { "enabled": true, "interval": 60, "template": "net_custom_metrics.j2"  }
