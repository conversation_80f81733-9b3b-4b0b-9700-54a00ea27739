---

- name: Install prerequisites from apt when Debian based
  apt: name={{item}} state=latest update_cache=yes
  with_items:
    - python-pip
    - unzip
    - curl
  when: ansible_os_family == "Debian"

# Amazon Linux 2 basic packages
- name: Install from yum
  yum:
    name: "{{ packages }}"
    state: latest
    update_cache: yes
  vars:
    packages:
      - libselinux-python
      - python2-pip
      - unzip
      - cronie
  when: ansible_distribution == 'Amazon'

# EPEL
- name: Install epel
  yum: name=https://dl.fedoraproject.org/pub/epel/epel-release-latest-7.noarch.rpm
  when: ansible_distribution == 'RedHat' or ansible_distribution == 'CentOS' or ansible_distribution == 'Amazon'
  ignore_errors: yes

#RedHat/Centos basic packages
- name: Install from yum
  yum: name={{item}} state=latest update_cache=yes
  with_items:
    - python-pip
    - unzip
    - cronie
  when: ansible_distribution == 'CentOS' or ansible_distribution == 'RedHat'

- name: Upgrade pip to latest version
  pip:
    name: pip
    state: latest
