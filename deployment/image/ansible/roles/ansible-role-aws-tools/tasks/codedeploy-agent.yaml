---
# http://docs.aws.amazon.com/codedeploy/latest/userguide/how-to-run-agent-install.html
- name: Install from yum when Red Hat based
  yum:
    name: ruby
    state: latest

- name: Download installer
  get_url:
    url: "https://aws-codedeploy-{{ ansible_ec2_placement_region }}.s3.amazonaws.com/latest/install"
    dest: /tmp/install-codedeploy-agent
    mode: o+x

- name: Execute installer
  command: /tmp/install-codedeploy-agent auto

- name: Start daemon
  service:
    name: codedeploy-agent
    state: restarted
    enabled: yes
