#!/bin/bash
# Move old service logs into S3

HOSTNAME=`hostname`
S3BUCKET="prd-taggstar-application-logs/reporting-service/${region}"
SERVICE_DIR=/home/<USER>/reporting-service
LOGS_DIR=${SERVICE_DIR}/logs
SCRIPT_LOG=/home/<USER>/cron/archivelogs.log

# Clear log with first line
echo "started" > ${SCRIPT_LOG}

FILES=`find ${LOGS_DIR} -name 'reporting-service.*.log.gz'`

for LOG in ${FILES}; do
aws s3 mv ${LOG} s3://${S3BUCKET}/ 2>&1 >> ${SCRIPT_LOG}
done

echo "finished" >> ${SCRIPT_LOG}
