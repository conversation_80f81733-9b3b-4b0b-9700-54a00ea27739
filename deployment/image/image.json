{"variables": {"aws_access_key": "", "aws_secret_key": "", "region": ""}, "builders": [{"type": "amazon-ebs", "access_key": "{{user `aws_access_key`}}", "secret_key": "{{user `aws_secret_key`}}", "region": "{{user `region`}}", "source_ami": "ami-0dacb0c129b49f529", "instance_type": "c5.large", "ssh_username": "ec2-user", "ami_name": "prd-reporting-service-ami-{{timestamp}}", "vpc_id": "vpc-0ecd32e88c1e24ef7", "subnet_id": "subnet-0ebf9173c38339cdb", "tags": {"Name": "reporting-service-img-builder"}}], "provisioners": [{"type": "ansible", "playbook_file": "./ansible/playbook.yaml"}, {"type": "file", "source": "./files/", "destination": "/tmp/"}, {"type": "shell", "skip_clean": true, "inline": ["sudo yum -y update", "mkdir /home/<USER>/cron", "region={{user `region`}} envsubst '${region}' < /tmp/archive-logs.sh.template > /home/<USER>/cron/archive-logs.sh", "chmod 755 /home/<USER>/cron/archive-logs.sh", "sudo chown root:root /tmp/archivelogs", "sudo chmod 644 /tmp/archivelogs", "sudo mv /tmp/archivelogs /etc/cron.d/", "sudo chown root:root /tmp/start-reporting-service", "sudo chmod 644 /tmp/start-reporting-service", "sudo mv /tmp/start-reporting-service /etc/cron.d/"]}]}