#!/usr/bin/env bash
# THIS SCRIPT EXPORTS SEVERAL VARIABLES EXPECTED TO BE USED IN OTHER CODE-DEPLOY SCRIPTS

export APP=reporting-service
export APP_HOME=/home/<USER>/${APP}

# Whether EC2 tag variables should be resolved
RES_TAGS=
# Assign values from getopts arguments to variables
while getopts ":t:" opt; do
  case $opt in
    t) RES_TAGS="$OPTARG"
    ;;
    \?) echo "Invalid option -$OPTARG" >&2
    ;;
  esac
done

# Fetch the IMDSv2 token
TOKEN=`curl -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600" --silent`
# Use the token to get the instance identity document and extract the region
export AWS_REGION=`curl -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/dynamic/instance-identity/document --silent | grep region | awk -F\" '{print $4}'`


if [ "${RES_TAGS}" = "true" ]; then
    INSTANCE_ID=$(ec2-metadata --instance-id | cut -f2 -d " ")

    # RESOLVE ENVIRONMENT
    # Get value of `Environment` EC2 tag for the instance
    ENV_TAG_NAME=Environment
    ENV_TAG_VALUE=$(aws ec2 describe-tags --filters "Name=resource-id,Values=$INSTANCE_ID" "Name=key,Values=$ENV_TAG_NAME" --region=$AWS_REGION --output=text | cut -f5)

    # Resolve the variables based on the Environment EC2 tag
    ENV=
    ADMIN_PORT=
    case "$ENV_TAG_VALUE" in
      qa)
        ENV=qa
        ADMIN_PORT=8101
        ;;
      prd|prod|production)
        ENV=prd
        ADMIN_PORT=8001
        ;;
    esac

    if [ -z ${ENV} ]; then
      >&2 echo 'ERROR Cannot resolve environment! Is Environment EC2 tag set correctly for the instance?'
      exit 1
    fi

    export ENV

fi