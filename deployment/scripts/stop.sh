#!/usr/bin/env bash

# GET ALL VARIABLES
. $(dirname "$0")/common.sh

# STOP APPLICATION
# Generic reusable code below
PID=$(pgrep -f ${APP})
TRIES=0
if [ ! -z "${PID}" ]; then
  kill ${PID}
  while [ ${TRIES} -le 4 ] && kill -0 ${PID} 2> /dev/null; do ((TRIES++)) && sleep 0.5; done; # sleep until killed or too many tries
  kill -9 ${PID} 2> /dev/null # kill it hardly in case it hangs
fi
# Overwrite potential 'No such process' error exit code
exit 0