#!/usr/bin/env bash

# GET APP_HOME AND ENV (-e option) variables
. $(dirname "$0")/common.sh -t true

JAVA_HOME=/usr/lib/jvm/java-22-amazon-corretto

# START APPLICATION
cd ${APP_HOME}
# Variable {app-version} is replaced with the current version by post-build shell action after successful Jenkins build
nohup $JAVA_HOME/bin/java -Xmx6144m -jar ./bin/${APP}-${app-version}.jar server config/${AWS_REGION}-${ENV}-${APP}.yml > /dev/null 2> /dev/null < /dev/null &
