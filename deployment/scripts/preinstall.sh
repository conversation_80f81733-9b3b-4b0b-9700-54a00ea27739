#!/usr/bin/env bash

# GET APP_HOME variable
. $(dirname "$0")/common.sh

# PREPARE BIN DIR
# Create bin and bin/previous dirs if do not exist
mkdir -p ${APP_HOME}/bin/previous
# Backup previous jar file
find ${APP_HOME}/bin -name '*.jar' -type f -exec mv {} ${APP_HOME}/bin/previous/ \;

# PREPARE CONFIG DIR
# Create config dir if does not exist
mkdir -p ${APP_HOME}/config
# Clean config dir by deleting all yaml files
find ${APP_HOME}/config -name '*.yml' -type f -exec rm {} \;