#!/usr/bin/env bash

. $(dirname "$0")/common.sh -t true

echo "Reporting Service version is: ${app-version}"

# VALIDATE - NEWLY DEPLOYED VERSION OF THE APPLICATION IS RUNNING
# If the version is not obtained then try it again few more times with pauses as it takes time for the application to fully initialize
TRIES=0
while [ ${TRIES} -le 3 ] && [ -z ${VERSION} ]; do
  sleep 10
  VERSION=`echo $(curl http://localhost:$ADMIN_PORT/version) | grep -Po '"'"application"'"\s*:\s*"\K([^"]*)' | cut -d "\"" -f 2`
  ((TRIES++))
done

# Error if versions are different
# Variable {app-version} is replaced with the current version by post-build shell action after successful Jenkins build
if [ "$VERSION" != "${app-version}" ]; then
  >&2 echo "ERROR Version $VERSION of the running application does not match deployed version ${app-version}"
  exit 1
fi