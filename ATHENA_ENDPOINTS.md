# Athena API Endpoints

This document describes the new Athena query endpoints that have been implemented for the reporting service.

## Base URL
```
/api/v1/athena/{siteKey}
```

## Common Parameters
All endpoints support the following query parameters:
- **startDate** (required): Start date for the query range (ISO-8601 format)
- **endDate** (required): End date for the query range (ISO-8601 format)
- **format** (optional): Response format, defaults to 'json'. Options: 'json', 'csv'

## Endpoints

### Orders

#### GET /api/v1/athena/{siteKey}/orders
Fetch orders data for a site with detailed information.

**Query Parameters:**
- `includeProducts` (boolean, optional): Include additional columns for order items (product_id, category, price, currency, quantity)
- `interval` (string, optional): Aggregates the data by time period. Options: 'hours', 'days', 'months', 'years'
- `experienceId` (string, optional): Filter by experience ID
- `experimentId` (string, optional): Filter by experiment ID
- `experimentGroup` (string, optional): Filter by experiment group (e.g., 'treatment', 'control')
- `deviceType` (string, optional): Filter by device type

**Response:** CSV or JSON containing order data with columns:
- timestamp
- module_run_id
- order_id
- device_type
- experiment_id
- experiment_state
- experiment_group
- experience_id
- locale
- order_total
- (if includeProducts=true) product_id, category, price, currency, quantity

### Product-specific

#### GET /api/v1/athena/{siteKey}/orders/byProduct/count
Count orders by product ID.

**Query Parameters:**
- `productIds` (string, optional): Comma-separated list of product IDs to filter by
- `interval` (string, optional): Aggregates the data by time period

**Response:**
```json
{
  "data": [
    {
      "product_id": "123456",
      "order_count": 42,
      "timestamp": "2025-04-10T00:00:00Z" // Only present when interval is specified
    }
  ]
}
```

#### GET /api/v1/athena/{siteKey}/products/impressions
Count product impressions per product ID.

**Query Parameters:**
- `productIds` (string, optional): Comma-separated list of product IDs
- `interval` (string, optional): Aggregates the data by time period

**Response:** Data with product_id and impressions count.

### Modules

#### GET /api/v1/athena/{siteKey}/modules/impressions
Count of module impressions.

**Query Parameters:**
- `moduleId` (string, optional): Filter by module ID
- `experienceId` (string, optional): Filter by experience ID
- `interval` (string, optional): Aggregates the data by time period

**Response:** Module impression counts.

#### GET /api/v1/athena/{siteKey}/modules/conversion
Daily order total from conversion module.

**Query Parameters:**
- `moduleId` (string, required): The module ID to get conversion data for

**Response:** Conversion data for the specified module.

#### GET /api/v1/athena/{siteKey}/modules/requests
Breakdown of API requests per month per experience ID.

**Query Parameters:**
- `optimized` (boolean, optional): Use optimised query for faster results
- `experienceId` (string, optional): Filter by experience ID
- `interval` (string, optional): Aggregates data by time period

**Response:** API request breakdown data.

### Sessions

#### GET /api/v1/athena/{siteKey}/sessions/count
Daily session count from sessions table.

**Query Parameters:**
- `experienceId` (string, optional): Filter by experience ID
- `experimentId` (string, optional): Filter by experiment ID
- `experimentGroup` (string, optional): Filter by experiment group (e.g., 'treatment', 'control')
- `deviceType` (string, optional): Filter by device type
- `interval` (string, optional): Aggregates the data by time period

**Response:** Session count data.

#### GET /api/v1/athena/{siteKey}/sessions/distinct
Count distinct orders by experience & device.

**Query Parameters:**
- `experienceId` (string, optional): Filter by experience ID
- `deviceType` (string, optional): Filter by device type

**Response:** Distinct order counts grouped by experience IDs and device types.

### Events

#### GET /api/v1/athena/{siteKey}/events/lookup
Lookup customer event data.

**Query Parameters:**
- `visitorId` (string, optional): Filter by visitor ID
- `eventType` (string, optional): Filter by event type

**Response:** Event data matching the criteria.

## Error Responses

All endpoints use standard HTTP status codes with the following error structure:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": { /* Additional context */ }
  }
}
```

**Error codes:**
- `INVALID_DATE_RANGE`: Start date must be before end date
- `MISSING_REQUIRED_PARAM`: A required parameter is missing
- `ATHENA_QUERY_ERROR`: Error executing the Athena query
- `RATE_LIMIT_EXCEEDED`: Too many requests

## Implementation Details

### Architecture
The new endpoints are built on top of the existing Athena infrastructure:

1. **EnhancedAthenaService**: Extends the base AthenaService with new query methods
2. **AthenaResource**: JAX-RS resource exposing all the new endpoints
3. **AthenaQueryParamRequest**: Request DTO handling all query parameters
4. **Response DTOs**: Structured response objects for each endpoint type

### Query Building
Each endpoint has a corresponding query building method that constructs the appropriate SQL query based on the request parameters. The queries are designed to work with the existing Athena tables:

- `conversion_module`: For order and conversion data
- `product_module`: For product-related data
- `module_aggregates`: For module performance data
- `session_aggregates`: For session data
- `custom_events`: For event lookup data

### Data Format Support
- **JSON**: Default response format for all endpoints
- **CSV**: Available for the orders endpoint with proper CSV headers

### Performance Considerations
- All queries include proper date partitioning using the `dt` column
- Optional time-based aggregation using `date_trunc` functions
- Filtering capabilities to reduce data volume
- Support for optimized queries where applicable

## Usage Examples

### Basic Orders Query
```bash
GET /api/v1/athena/example-site/orders?startDate=2025-01-01&endDate=2025-01-31
```

### Orders with Products and Filtering
```bash
GET /api/v1/athena/example-site/orders?startDate=2025-01-01&endDate=2025-01-31&includeProducts=true&deviceType=mobile&format=csv
```

### Product Order Counts with Time Aggregation
```bash
GET /api/v1/athena/example-site/orders/byProduct/count?startDate=2025-01-01&endDate=2025-01-31&interval=days
```

### Module Conversion Data
```bash
GET /api/v1/athena/example-site/modules/conversion?startDate=2025-01-01&endDate=2025-01-31&moduleId=module-123
```

## Future Enhancements

1. **Caching**: Implement query result caching for frequently requested data
2. **Async Processing**: Support for long-running queries with job status tracking
3. **Query Templates**: Pre-defined query templates for common use cases
4. **Rate Limiting**: Implement proper rate limiting for API endpoints
5. **Query Optimization**: Further optimize SQL queries based on usage patterns
6. **Additional Tables**: Support for more Athena tables as they become available

