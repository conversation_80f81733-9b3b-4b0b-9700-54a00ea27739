env: prd

server:
  applicationConnectors:
    - type: http
      port: 8000
  adminConnectors:
    - type: http
      port: 8001

jerseyClient:
  timeout: 60s

athena:
    database: 'taggstar_reporting'
    outputBucket: 's3://aws-athena-query-results-682109183784-us-east-2/'

amazon:
  profile: mfa
  region: us-east-2

cloudWatch:
  enable: true
  prefix: Prd
  applicationName: ReportingService
  enableJVMMetrics: true

euConfigService:
  address:
    host: prdconfig01.taggstar.net
    port: 8080

usConfigService:
  address:
    host: *************
    port: 8080

bayesianService:
  address:
    host: localhost
    port: 8066

configServiceAdminPort: 8001

logging:
  level: INFO

  loggers:
    com.taggstar.dropwizard.ext.log.LoggedFilter: DEBUG
    org.jooq: DEBUG

  appenders:
    - type: console
      timeZone: UTC
      includeCallerData: true
      logFormat: '%d{ISO8601} %5p [%mdc{requestId}] [%mdc{siteKey}] %c{1}:%line - %m%n'
