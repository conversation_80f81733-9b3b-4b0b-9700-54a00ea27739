# This is an appspec.yml template file for use with AWS CodeDeploy.
# The lines in this template starting with the hashtag symbol are
#   instructional comments and can be safely left in the file or
#   ignored.
# For help completing this file, see the "AppSpec File Reference" in the
#   "AWS CodeDeploy User Guide" at
#   http://docs.aws.amazon.com/codedeploy/latest/userguide/app-spec-ref.html
version: 0.0

# Specify "os: linux" if this revision targets Amazon Linux,
#   Red Hat Enterprise Linux (RHEL), or Ubuntu Server
#   instances.
# Specify "os: windows" if this revision targets Windows Server instances.
# (You cannot specify both "os: linux" and "os: windows".)
os: linux

# During the Install deployment lifecycle event (which occurs between the
#   BeforeInstall and AfterInstall events), copy the specified files
#   in "source" starting from the root of the revision's file bundle
#   to "destination" on the Amazon EC2 instance.
# Specify multiple "source" and "destination" pairs if you want to copy
#   from multiple sources or to multiple destinations.
#
# Variable {app-version} is replaced with the current version by post-build shell action after successful Jenkins build
files:
  - source: target/reporting-service-${app-version}.jar
    destination: /home/<USER>/reporting-service/bin
  - source: config
    destination: /tmp/deployment/reporting-service/config
  # These scripts below are copied only to make them available to manual execution. This action is not required for auto-deployment.
  - source: deployment/scripts/common.sh
    destination: /home/<USER>/reporting-service
  - source: deployment/scripts/start.sh
    destination: /home/<USER>/reporting-service
  - source: deployment/scripts/stop.sh
    destination: /home/<USER>/reporting-service

# For deployments to Amazon Linux, Ubuntu Server, or RHEL instances,
#   you can specify a "permissions"
#   section here that describes special permissions to apply to the files
#   in the "files" section as they are being copied over to
#   the Amazon EC2 instance.
permissions:
  - object: /home/<USER>/reporting-service
    owner: ec2-user
    group: ec2-user
    except: [common.sh, start.sh, stop.sh]
  - object: /tmp/deployment/reporting-service
    owner: ec2-user
    group: ec2-user
  - object: /home/<USER>/reporting-service
    pattern: "*.sh"
    owner: ec2-user
    group: ec2-user
    mode: 755
    type:
      - file

# For each deployment lifecycle event, specify multiple "location" entries
#   if you want to run multiple scripts during that event.
# You can specify "timeout" as the number of seconds to wait until failing the deployment
#   if the specified scripts do not run within the specified time limit for the
#   specified event. For example, 900 seconds is 15 minutes. If not specified,
#   the default is 1800 seconds (30 minutes).
#   Note that the maximum amount of time that all scripts must finish executing
#   for each individual deployment lifecycle event is 3600 seconds (1 hour).
#   Otherwise, the deployment will stop and AWS CodeDeploy will consider the deployment
#   to have failed to the Amazon EC2 instance. Make sure that the total number of seconds
#   that are specified in "timeout" for all scripts in each individual deployment
#   lifecycle event does not exceed a combined 3600 seconds (1 hour).
# For deployments to Amazon Linux, Ubuntu Server, or RHEL instances,
#   you can specify "runas" in an event to
#   run as the specified user. For more information, see the documentation.
#   If you are deploying to Windows Server instances,
#   remove "runas" altogether.
hooks:
# During the ApplicationStop deployment lifecycle event, run the commands
#   in the script specified in "location" starting from the root of the
#   revision's file bundle.
# To troubleshoot a deployment that fails during the ApplicationStop deployment lifecycle event:
#   http://docs.aws.amazon.com/codedeploy/latest/userguide/troubleshooting-deployments.html#troubleshooting-deployments-applicationstop
  ApplicationStop:
    - location: deployment/scripts/stop.sh
      timeout: 5
      runas: ec2-user
# During the BeforeInstall deployment lifecycle event, run the commands
#   in the script specified in "location".
  BeforeInstall:
    - location: deployment/scripts/preinstall.sh
      runas: ec2-user
# During the ApplicationInstall deployment lifecycle event, run the commands
#   in the script specified in "location".
# During the AfterInstall deployment lifecycle event, run the commands
#   in the script specified in "location".
  AfterInstall:
    - location: deployment/scripts/postinstall.sh
      timeout: 3
      runas: ec2-user
  ApplicationStart:
    - location: deployment/scripts/start.sh
      timeout: 5
      runas: ec2-user
# During the ValidateService deployment lifecycle event, run the commands
#   in the script specified in "location".
  ValidateService:
    - location: deployment/scripts/validation.sh
      timeout: 15
      runas: ec2-user