package com.taggstar.reporting.service.resources;

import com.taggstar.dropwizard.ext.cw.Monitored;
import com.taggstar.dropwizard.ext.log.Logged;
import com.taggstar.reporting.service.config.ConfigServerClient;
import com.taggstar.reporting.service.config.account.AccountConfig;
import com.taggstar.reporting.service.config.account.Contract;
import com.taggstar.reporting.service.domain.contractusage.ContractUsageReport;
import com.taggstar.reporting.service.domain.contractusage.ContractUsageReportGenerator;
import com.taggstar.reporting.service.dto.request.param.ContractUsageParamRequest;
import com.taggstar.reporting.service.exceptions.ConfigServerException;
import com.taggstar.reporting.service.model.aggregate.RequestsAggregate;
import com.taggstar.reporting.service.services.MultiRegionDataAggregator;
import jakarta.validation.Valid;
import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

import static com.taggstar.reporting.service.ReportingApplication.VERSION;

/**
 * Resource for retrieving contract usage reports across multiple AWS regions.
 * This resource aggregates data from both EU and US regions to provide a comprehensive
 * view of API usage across all sites in an account.
 */
@Path("/api/" + VERSION + "/requests/usage")
@Produces(MediaType.APPLICATION_JSON)
@Logged(slow = 1000)
@Monitored
public class ContractUsageResource {

    private static final Logger log = LoggerFactory.getLogger(ContractUsageResource.class);

    private final MultiRegionDataAggregator requestAggregateDao;
    private final ConfigServerClient configServerClient;
    private final ContractUsageReportGenerator reportGenerator;

    public ContractUsageResource(MultiRegionDataAggregator requestAggregateDao,
                                ConfigServerClient configServerClient,
                                ContractUsageReportGenerator reportGenerator) {
        this.requestAggregateDao = requestAggregateDao;
        this.configServerClient = configServerClient;
        this.reportGenerator = reportGenerator;
    }

    /**
     * Retrieves a comprehensive API usage report for a contract across all regions.
     * 
     * @param req the request parameters containing account ID and contract start date
     * @return contract usage report with aggregated data from all regions
     * @throws RuntimeException if the contract is not found
     * @throws ConfigServerException if there are issues with configuration or data access
     */
    @GET
    public ContractUsageReport getApiUsageReport(@Valid @BeanParam ContractUsageParamRequest req) {
        log.debug("Generating API usage report for account: {}, contract start date: {}", 
                req.getAccountId(), req.getContractStartDate());

        try {
            AccountConfig accountConfig = configServerClient.obtainAccountConfiguration(req.getAccountId());

            Contract contract = accountConfig.getContractWithStartDate(req.getContractStartDate())
                    .orElseThrow(() -> new RuntimeException(
                            "Contract with start date " + req.getContractStartDate() + " not found"));

            List<RequestsAggregate> aggregates = requestAggregateDao.aggregateRequestsAcrossRegions(contract, accountConfig);
            
            log.info("Successfully aggregated {} request aggregates for contract starting {}", 
                    aggregates.size(), contract.startDate());

            return reportGenerator.generateUsageReport(contract, aggregates);
            
        } catch (Exception e) {
            log.error("Failed to generate API usage report for account: {}, error: {}", 
                    req.getAccountId(), e.getMessage(), e);
            throw e;
        }
    }
}
