package com.taggstar.reporting.service.services;

import com.taggstar.reporting.service.config.account.AccountConfig;
import com.taggstar.reporting.service.config.account.Contract;
import com.taggstar.reporting.service.db.dao.aggregate.requests.RequestAggregateCriteria;
import com.taggstar.reporting.service.db.dao.aggregate.requests.RequestAggregateDao;
import com.taggstar.reporting.service.config.RegionConfiguration;
import com.taggstar.reporting.service.exceptions.ConfigServerException;
import com.taggstar.reporting.service.model.aggregate.RequestsAggregate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.regions.Region;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.taggstar.reporting.service.model.IntervalUnit.DAY;
import static java.util.stream.Collectors.mapping;

/**
 * Service responsible for aggregating data across multiple AWS regions.
 * Handles the complexity of fetching data from different regional databases
 * and combining the results for cross-region reporting.
 */
public class MultiRegionDataAggregator {
    
    private static final Logger log = LoggerFactory.getLogger(MultiRegionDataAggregator.class);
    
    private final Map<Region, RequestAggregateDao> regionToRequestAggregateDao;
    
    public MultiRegionDataAggregator(Map<Region, RequestAggregateDao> regionToRequestAggregateDao) {
        this.regionToRequestAggregateDao = regionToRequestAggregateDao;
    }
    
    /**
     * Aggregates request data across all regions for a given contract and account configuration.
     * 
     * @param contract the contract to fetch data for
     * @param accountConfig the account configuration containing site information
     * @return combined list of request aggregates from all regions
     */
    public List<RequestsAggregate> aggregateRequestsAcrossRegions(Contract contract, AccountConfig accountConfig) {
        log.debug("Aggregating requests across regions for contract starting {}", contract.startDate());
        
        Map<Region, List<String>> regionToSiteKeys = groupSitesByRegion(accountConfig);
        List<RequestsAggregate> allAggregates = new ArrayList<>();
        
        for (Map.Entry<Region, List<String>> entry : regionToSiteKeys.entrySet()) {
            Region region = entry.getKey();
            List<String> siteKeys = entry.getValue();
            
            if (!siteKeys.isEmpty()) {
                try {
                    List<RequestsAggregate> regionAggregates = fetchRegionData(region, siteKeys, contract);
                    allAggregates.addAll(regionAggregates);
                    log.debug("Fetched {} aggregates from region {}", regionAggregates.size(), region);
                } catch (Exception e) {
                    log.error("Failed to fetch data from region {}: {}", region, e.getMessage(), e);
                    throw new ConfigServerException("Failed to fetch data from region " + region, e);
                }
            }
        }
        
        log.info("Successfully aggregated {} total request aggregates across {} regions", 
                allAggregates.size(), regionToSiteKeys.size());
        
        return allAggregates;
    }
    
    /**
     * Groups site keys by their AWS region.
     * 
     * @param accountConfig the account configuration
     * @return map of region to list of site keys
     */
    private Map<Region, List<String>> groupSitesByRegion(AccountConfig accountConfig) {
        return accountConfig.siteKeysWithAwsRegion().stream()
                .collect(Collectors.groupingBy(
                        siteKey -> Region.of(siteKey.awsRegion()),
                        mapping(
                                AccountConfig.SiteKey::siteKey,
                                Collectors.toList()
                        )
                ));
    }
    
    /**
     * Fetches data for a specific region and set of site keys.
     * 
     * @param region the AWS region
     * @param siteKeys the site keys for this region
     * @param contract the contract to fetch data for
     * @return list of request aggregates for this region
     */
    private List<RequestsAggregate> fetchRegionData(Region region, List<String> siteKeys, Contract contract) {
        // Validate that the region is supported
        RegionConfiguration.validateRegion(region.id());
        
        RequestAggregateDao dao = regionToRequestAggregateDao.get(region);
        if (dao == null) {
            throw new ConfigServerException("No DAO configured for region: " + region);
        }
        
        RequestAggregateCriteria criteria = buildCriteria(siteKeys, contract);
        return dao.readRequestsAggregated(criteria);
    }
    
    /**
     * Builds the criteria for fetching request aggregates.
     * 
     * @param siteKeys the site keys to include
     * @param contract the contract to fetch data for
     * @return configured RequestAggregateCriteria
     */
    private RequestAggregateCriteria buildCriteria(List<String> siteKeys, Contract contract) {
        RequestAggregateCriteria criteria = new RequestAggregateCriteria();
        
        criteria.setSiteKeys(siteKeys);
        criteria.setStartDate(contract.startDate());
        criteria.setEndDate(determineEndDate(contract));
        criteria.setIntervalUnit(DAY);
        criteria.setFetchSite(true);
        
        return criteria;
    }
    
    /**
     * Determines the end date for the query based on contract type.
     * 
     * @param contract the contract
     * @return the end date to use
     */
    private LocalDate determineEndDate(Contract contract) {
        if ("monthly_rolling".equals(contract.type())) {
            return LocalDate.now();
        }
        return contract.endDate();
    }
}
