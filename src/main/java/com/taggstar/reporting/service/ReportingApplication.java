package com.taggstar.reporting.service;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.taggstar.config.AmazonConfig;
import com.taggstar.dropwizard.ext.cw.*;
import com.taggstar.dropwizard.ext.error.CustomExceptionMapper;
import com.taggstar.dropwizard.ext.lifecycle.CloseableToManagedAdapter;
import com.taggstar.dropwizard.ext.log.CustomHeaderFilter;
import com.taggstar.dropwizard.ext.log.LoggedFeature;
import com.taggstar.dropwizard.ext.log.SiteLoggerLevelFilter;
import com.taggstar.dropwizard.ext.log.SiteLoggerLevelTask;
import com.taggstar.dropwizard.ext.util.HostnameProvider;
import com.taggstar.dropwizard.ext.util.SharedInstance;
import com.taggstar.reporting.service.athena.AthenaClientFactory;
import com.taggstar.reporting.service.athena.AthenaService;
import com.taggstar.reporting.service.config.ConfigServerClient;
import com.taggstar.reporting.service.config.LocalConfiguration;
import com.taggstar.reporting.service.config.SecretCredentials;
import com.taggstar.reporting.service.db.CachingDatabaseProvider;
import com.taggstar.reporting.service.db.DataSourceFactory;
import com.taggstar.reporting.service.db.DatabaseInfoProvider;
import com.taggstar.reporting.service.db.DatabaseProvider;
import com.taggstar.reporting.service.db.dao.aggregate.messages.MessageAggregateDao;
import com.taggstar.reporting.service.db.dao.aggregate.modules.ModuleAggregateDao;
import com.taggstar.reporting.service.db.dao.aggregate.requests.RequestAggregateDao;
import com.taggstar.reporting.service.db.dao.aggregate.sessions.SessionAggregateDao;
import com.taggstar.reporting.service.db.dao.aggregate.tags.TagAggregateDao;
import com.taggstar.reporting.service.db.dao.experiment.ExperimentDao;
import com.taggstar.reporting.service.db.dao.experiment.ExperimentDataDao;
import com.taggstar.reporting.service.db.dao.experiment.reports.ExperimentReportsDao;
import com.taggstar.reporting.service.db.dao.order.OrderDao;
import com.taggstar.reporting.service.deserializers.BayesianResultDeserializer;
import com.taggstar.reporting.service.deserializers.DataPointDeserializer;
import com.taggstar.reporting.service.deserializers.ReportTableRowDeserializer;
import com.taggstar.reporting.service.deserializers.StatisticsDeserializer;
import com.taggstar.reporting.service.domain.NullObjects;
import com.taggstar.reporting.service.domain.bayesian.BayesianResult;
import com.taggstar.reporting.service.domain.bayesian.BayesianServiceClient;
import com.taggstar.reporting.service.domain.contractusage.ContractUsageReportGenerator;
import com.taggstar.reporting.service.domain.experiment.StoredExperimentReportService;
import com.taggstar.reporting.service.dto.request.LocalDateParamProvider;
import com.taggstar.reporting.service.dto.request.LocaleParamProvider;
import com.taggstar.reporting.service.dto.request.ZoneIdParamProvider;
import com.taggstar.reporting.service.dto.response.reports.experiment.Row;
import com.taggstar.reporting.service.dto.response.reports.series.DataPoint;
import com.taggstar.reporting.service.dto.response.reports.series.statistics.Statistics;
import com.taggstar.reporting.service.mappers.BadRequestExceptionMapper;
import com.taggstar.reporting.service.mappers.InternalServerErrorExceptionMapper;
import com.taggstar.reporting.service.resources.*;
import com.taggstar.reporting.service.services.MultiRegionDataAggregator;
import com.taggstar.reporting.service.util.CSVMessageBodyWriter;
import io.dropwizard.bundles.version.VersionBundle;
import io.dropwizard.bundles.version.VersionSupplier;
import io.dropwizard.bundles.version.suppliers.MavenVersionSupplier;
import io.dropwizard.client.JerseyClientBuilder;
import io.dropwizard.core.Application;
import io.dropwizard.core.setup.Bootstrap;
import io.dropwizard.core.setup.Environment;
import jakarta.ws.rs.client.Client;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.*;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.athena.AthenaClient;
import software.amazon.awssdk.services.cloudwatch.CloudWatchClient;
import software.amazon.awssdk.services.cloudwatch.model.Dimension;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.taggstar.dropwizard.ext.cw.MetricDefinition.CommonDimNames.HOSTNAME;
import static com.taggstar.dropwizard.ext.log.CustomHeaders.REQUEST_ID;
import static com.taggstar.dropwizard.ext.log.CustomHeaders.SITE_KEY;
import static org.slf4j.Logger.ROOT_LOGGER_NAME;
import static software.amazon.awssdk.regions.Region.EU_WEST_1;
import static software.amazon.awssdk.regions.Region.US_EAST_2;

/**
 * The application/service entry point. Instantiates top level objects, wires them together and performs registrations.
 */
public class ReportingApplication extends Application<LocalConfiguration> {

    public static final String VERSION = "v4";

    private static final Logger log = LoggerFactory.getLogger(ReportingApplication.class);

    public static void main(String[] args) throws Exception {
        new ReportingApplication().run(args);
    }

    @Override
    public String getName() {
        return "Reporting Service (id: rep-svc)";
    }

    @Override
    public void initialize(Bootstrap<LocalConfiguration> bootstrap) {
        super.initialize(bootstrap);
        // Adds /version resource for version validation
        VersionSupplier supplier = new MavenVersionSupplier("taggstar", "reporting-service");
        bootstrap.addBundle(new VersionBundle<>(supplier));
    }

    /**
     * This method is executed on application startup.
     *
     * @param config application config
     * @param env environment info
     */
    @Override
    public void run(LocalConfiguration config, Environment env) {
        log.atInfo().log("event=[reporting_service_started] config=[{}]", config);
        customizeJsonObjectMapper(env);
        registerCustomProviders(env);
        env.admin().addTask(new SiteLoggerLevelTask(registerSiteLoggerLevelFilter()));
        AwsCredentialsProvider awsCredentials = createAWSCredentials(config.getAmazon());
        registerMetricsReporter(config, env, awsCredentials);

        // Create a shared Jersey client
        Client jerseyClient = createJerseyClient(env, config);

        ConfigServerClient euConfigServerClient = createAndRegisterEuConfigServerClient(env, config, jerseyClient);
        ConfigServerClient usConfigServerClient = createAndRegisterUSConfigServerClient(env, config, jerseyClient);
        Map<Region, ConfigServerClient> configServerClients = Map.of(EU_WEST_1, euConfigServerClient, US_EAST_2, usConfigServerClient);

        BayesianServiceClient bayesianServiceClient = createBayesianServiceClient(env, config, jerseyClient);
        AthenaService athenaService = createAthenaService(awsCredentials, config);

        Map<Region, SecretCredentials> secretClients = setupSecretCredentials(config.getEnv(), env, awsCredentials);

        DatabaseProvider euDatabaseProvider = createDBIProvider(env, secretClients.get(EU_WEST_1), euConfigServerClient);
        DatabaseProvider usDatabaseProvider = createDBIProvider(env, secretClients.get(US_EAST_2), usConfigServerClient);
        Map<Region, DatabaseProvider> databaseProviders = Map.of(EU_WEST_1, euDatabaseProvider, US_EAST_2, usDatabaseProvider);

        registerResources(Region.of(config.getAmazon().getRegion()), env, athenaService, databaseProviders, configServerClients, bayesianServiceClient);
    }

    private Map<Region, SecretCredentials> setupSecretCredentials(String environment, Environment env, AwsCredentialsProvider awsCredentials) {
        SecretsManagerClient euSecretClient = SecretsManagerClient.builder().region(EU_WEST_1).credentialsProvider(awsCredentials).build();
        env.lifecycle().manage(new CloseableToManagedAdapter(euSecretClient));
        SecretsManagerClient usSecretClient = SecretsManagerClient.builder().region(US_EAST_2).credentialsProvider(awsCredentials).build();
        env.lifecycle().manage(new CloseableToManagedAdapter(usSecretClient));

        SecretCredentials euSecretCredentials = new SecretCredentials(environment, euSecretClient, env.getObjectMapper());
        SecretCredentials usSecretCredentials = new SecretCredentials(environment, usSecretClient, env.getObjectMapper());
        return Map.of(EU_WEST_1, euSecretCredentials, US_EAST_2, usSecretCredentials);
    }

    /**
     * Creates a Jersey client to be shared across services
     */
    private Client createJerseyClient(Environment env, LocalConfiguration config) {
        return new JerseyClientBuilder(env).using(config.getJerseyClient()).build(getName());
    }

    private AthenaService createAthenaService(AwsCredentialsProvider awsCredentials, LocalConfiguration config) {
        AthenaClient athenaClient = new AthenaClientFactory(awsCredentials)
                .createClient(Region.of(config.getAmazon().getRegion()));
        return new AthenaService(athenaClient, config.getAthena().getOutputBucket(), config.getAthena().getDatabase());
    }

    private static void customizeJsonObjectMapper(Environment env) {
        ObjectMapper mapper = env.getObjectMapper();

        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        mapper.registerModule(experimentJsonModule(mapper));

        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.enable(SerializationFeature.INDENT_OUTPUT);
    }

    private static SimpleModule experimentJsonModule(ObjectMapper mapper) {
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Row.class, new ReportTableRowDeserializer());
        module.addDeserializer(Statistics.class, new StatisticsDeserializer(mapper));
        module.addDeserializer(DataPoint.class, new DataPointDeserializer());
        module.addDeserializer(BayesianResult.class, new BayesianResultDeserializer());
        return module;
    }

    private static void registerCustomProviders(Environment env) {
        env.jersey().register(new LocalDateParamProvider());
        env.jersey().register(new ZoneIdParamProvider());
        env.jersey().register(new LocaleParamProvider());
        env.jersey().register(new CustomHeaderFilter(REQUEST_ID));
        env.jersey().register(new CustomHeaderFilter(SITE_KEY));
        env.jersey().register(LoggedFeature.class);
        env.jersey().register(MonitoredFeature.class);
        env.jersey().register(CustomExceptionMapper.class);
        env.jersey().register(BadRequestExceptionMapper.class);
        env.jersey().register(InternalServerErrorExceptionMapper.class);
        env.jersey().register(CSVMessageBodyWriter.class);
    }

    private static SiteLoggerLevelFilter registerSiteLoggerLevelFilter() {
        SiteLoggerLevelFilter filter = new SiteLoggerLevelFilter();

        ch.qos.logback.classic.Logger rootLogger = (ch.qos.logback.classic.Logger) LoggerFactory.getLogger(ROOT_LOGGER_NAME);
        rootLogger.getLoggerContext().addTurboFilter(filter);
        filter.start();

        return filter;
    }

    private static AwsCredentialsProvider createAWSCredentials(AmazonConfig amazonConfig) {
        if (amazonConfig.hasKey()) {
            return StaticCredentialsProvider.create(AwsBasicCredentials.create(amazonConfig.getKey().getAccess(), amazonConfig.getKey().getSecret()));
        } else if (amazonConfig.hasProfile()) {
            return ProfileCredentialsProvider.create(amazonConfig.getProfile());
        } else {
            return InstanceProfileCredentialsProvider.create();
        }
    }

    private static void registerMetricsReporter(LocalConfiguration conf, Environment env, AwsCredentialsProvider awsCredentials) {
        CloudWatchClient cwClient = CloudWatchClient.builder().credentialsProvider(awsCredentials).region(Region.of(conf.getAmazon().getRegion())).build();

        SharedInstance<MetricRegistry> metrics = new SharedInstance<>(MetricRegistry::new);
        MetricsReporter metricsReporter = new MetricsReporterBuilder()
                .setCloudWatch(cwClient)
                .setNamespace(conf.getCloudWatch().getFullNamespace())
                .setGlobalMetrics(env.metrics())
                .setIntervalMetrics(metrics)
                .addMetricRules(metricRules(conf.getCloudWatch().isEnableJVMMetrics()))
                .addInstanceDimensions(createInstanceDimensions(conf))
                .copyInstanceDimensionMetrics(conf.getCloudWatch().isCopyInstanceDimensionsMetrics())
                .build();

        metricsReporter.setEnabled(conf.getCloudWatch().isEnable());
        env.jersey().register(new MetricsBinder(metrics));
        env.lifecycle().manage(metricsReporter);
    }

    /**
     * Defines which metrics are reported to CloudWatch
     *
     * @return metric rules
     */
    private static List<MetricRule> metricRules(boolean reportJVMMetrics) {
        TaggstarMetricRules rules = new TaggstarMetricRules()
                .reportTaggstarMetrics()
                .reportJedisMetrics()
                .reportResponses();

        if (reportJVMMetrics) {
            rules = rules.reportJVMMemoryUsage().reportJVMMemoryUsed();
        }

        return rules.build();
    }

    private static List<Dimension> createInstanceDimensions(LocalConfiguration config) {
        List<Dimension> dims = new ArrayList<>(2);

        if (config.getCloudWatch().isHostnameDimension()) {
            dims.add(Dimension.builder().name(HOSTNAME.getValue()).value(new HostnameProvider.Default().getHostname()).build());
        }

        return dims;
    }

    private ConfigServerClient createAndRegisterEuConfigServerClient(Environment env, LocalConfiguration config, Client jerseyClient) {
        ConfigServerClient csClient = new ConfigServerClient(
                config.getEuConfigService().getAddress().getHost(),
                config.getEuConfigService().getAddress().getPort(),
                config.configServiceAdminPort(),
                jerseyClient, env.getObjectMapper());
        env.healthChecks().register("euConfigServer", csClient);
        env.lifecycle().manage(csClient);
        return csClient;
    }

    private ConfigServerClient createAndRegisterUSConfigServerClient(Environment env, LocalConfiguration config, Client jerseyClient) {
        ConfigServerClient csClient = new ConfigServerClient(
                config.getUsConfigService().getAddress().getHost(),
                config.getUsConfigService().getAddress().getPort(),
                config.configServiceAdminPort(),
                jerseyClient, env.getObjectMapper());
        env.healthChecks().register("usConfigServer", csClient);
        env.lifecycle().manage(csClient);
        return csClient;
    }

    private BayesianServiceClient createBayesianServiceClient(Environment env, LocalConfiguration config, Client jerseyClient) {
        BayesianServiceClient bayesianServiceClient = new BayesianServiceClient(
                config.getBayesianService().getAddress().getHost(),
                config.getBayesianService().getAddress().getPort(),
                config.bayesianServiceAdminPort(),
                jerseyClient);
        env.healthChecks().register("bayesianService", bayesianServiceClient);
        env.lifecycle().manage(bayesianServiceClient);
        return bayesianServiceClient;
    }

    private static DatabaseProvider createDBIProvider(Environment env, DatabaseInfoProvider dbInfoProvider, ConfigServerClient configServerClient) {
        DataSourceFactory dbiFact = new DataSourceFactory(env);
        return new CachingDatabaseProvider(dbInfoProvider, dbiFact, configServerClient);
    }

    private static void registerResources(Region currentAwsRegion,
                                          Environment env,
                                          AthenaService athenaService,
                                          Map<Region, DatabaseProvider> databaseProviders,
                                          Map<Region, ConfigServerClient> configServerClients,
                                          BayesianServiceClient bayesianServiceClient) {
        DatabaseProvider databaseProvider = databaseProviders.get(currentAwsRegion);
        ConfigServerClient configServerClient = configServerClients.get(currentAwsRegion);

        Map<Region, RequestAggregateDao> requestAggregateDaos = Map.of(
                EU_WEST_1, new RequestAggregateDao(databaseProviders.get(EU_WEST_1)),
                US_EAST_2, new RequestAggregateDao(databaseProviders.get(US_EAST_2)));
        RequestAggregateDao requestAggregateDao = new RequestAggregateDao(databaseProvider);

        ModuleAggregateDao moduleAggregateDao = new ModuleAggregateDao(databaseProvider);
        MessageAggregateDao messageAggregateDao = new MessageAggregateDao(databaseProvider);
        SessionAggregateDao sessionAggregateDao = new SessionAggregateDao(databaseProvider);
        OrderDao orderDao = new OrderDao(athenaService);
        TagAggregateDao tagAggregateDao = new TagAggregateDao(databaseProvider);
        ExperimentReportsDao reportDao = new ExperimentReportsDao(databaseProvider, env.getObjectMapper());

        StoredExperimentReportService storedExperimentReportService = new StoredExperimentReportService(reportDao);

        ConversionModuleResource conversionModuleRes = new ConversionModuleResource(moduleAggregateDao, orderDao);
        BasketModuleResource basketModuleRes = new BasketModuleResource(moduleAggregateDao, messageAggregateDao);
        CategoryModuleResource categoryModuleRes = new CategoryModuleResource(moduleAggregateDao, messageAggregateDao);
        ProductModuleResource productModuleRes = new ProductModuleResource(moduleAggregateDao, messageAggregateDao);
        AssistantModuleResource assistantModuleResource = new AssistantModuleResource(configServerClient, moduleAggregateDao);
        RecsModuleResource recsModuleResource = new RecsModuleResource(moduleAggregateDao);
        CustomEventModuleResource customEventModuleResource = new CustomEventModuleResource(moduleAggregateDao);

        env.jersey().register(conversionModuleRes);
        env.jersey().register(basketModuleRes);
        env.jersey().register(categoryModuleRes);
        env.jersey().register(productModuleRes);
        env.jersey().register(recsModuleResource);
        env.jersey().register(new CombinedModulesResource(conversionModuleRes, basketModuleRes, categoryModuleRes, productModuleRes, assistantModuleResource, customEventModuleResource));
        env.jersey().register(new RequestsResource(requestAggregateDao));
        MultiRegionDataAggregator multiRegionDataAggregator = new MultiRegionDataAggregator(requestAggregateDaos);
        ContractUsageReportGenerator contractUsageReportGenerator = new ContractUsageReportGenerator();
        env.jersey().register(new ContractUsageResource(multiRegionDataAggregator, configServerClient, contractUsageReportGenerator));
        env.jersey().register(new DataValidationResource(moduleAggregateDao));
        env.jersey().register(new ExperimentResource(new ExperimentDao(databaseProvider)));
        ExperimentReportResource experimentReportResource = new ExperimentReportResource(
                configServerClient,
                new ExperimentDataDao(sessionAggregateDao, moduleAggregateDao, NullObjects.CATEGORY),
                storedExperimentReportService,
                bayesianServiceClient);
        env.jersey().register(experimentReportResource);
        env.jersey().register(assistantModuleResource);
        env.jersey().register(customEventModuleResource);
        env.jersey().register(new TagReportResource(tagAggregateDao, configServerClient));
        env.jersey().register(new StoredExperimentReportResource(env.getObjectMapper(), storedExperimentReportService));
        env.jersey().register(new BayesianServiceResource(bayesianServiceClient));
        env.jersey().register(new AthenaResource(athenaService));
    }
}