package com.taggstar.reporting.service.config.account;

import com.taggstar.dropwizard.ext.doc.DTO;
import net.jcip.annotations.Immutable;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Site configuration object
 */
@Immutable
@DTO
public record AccountConfig(
        List<SiteKey> siteKeysWithAwsRegion,
        CoreTaggstarMessagingService coreTaggstarMessagingService) {

    public Optional<Contract> getContractWithStartDate(LocalDate startDate) {
        return coreTaggstarMessagingService.contracts().stream()
                .filter(contract -> contract.startDate().equals(startDate))
                .findAny();
    }

    public List<String> getSiteKeys() {
        return siteKeysWithAwsRegion().stream()
                .map(AccountConfig.SiteKey::siteKey)
                .toList();
    }

    public record SiteKey(String awsRegion, String siteKey) {
    }

}
