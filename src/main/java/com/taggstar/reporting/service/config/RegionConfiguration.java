package com.taggstar.reporting.service.config;

import java.util.List;
import java.util.Map;

/**
 * Configuration class for managing region-specific settings and validations.
 * This centralizes region-related logic and makes it easier to add new regions in the future.
 */
public class RegionConfiguration {
    
    public static final String EU_WEST_1 = "eu-west-1";
    public static final String US_EAST_2 = "us-east-2";
    
    private static final List<String> SUPPORTED_REGIONS = List.of(EU_WEST_1, US_EAST_2);
    
    /**
     * Validates that a region is supported by the system.
     * 
     * @param region the region to validate
     * @throws IllegalArgumentException if the region is not supported
     */
    public static void validateRegion(String region) {
        if (!SUPPORTED_REGIONS.contains(region)) {
            throw new IllegalArgumentException("Unsupported AWS region: " + region + 
                    ". Supported regions: " + SUPPORTED_REGIONS);
        }
    }
    
    /**
     * Gets all supported regions.
     * 
     * @return list of supported region names
     */
    public static List<String> getSupportedRegions() {
        return List.copyOf(SUPPORTED_REGIONS);
    }
    
    /**
     * Checks if a region is supported.
     * 
     * @param region the region to check
     * @return true if the region is supported, false otherwise
     */
    public static boolean isRegionSupported(String region) {
        return SUPPORTED_REGIONS.contains(region);
    }
    
    /**
     * Creates a map of region names to their display names.
     * 
     * @return map of region names to display names
     */
    public static Map<String, String> getRegionDisplayNames() {
        return Map.of(
                EU_WEST_1, "Europe (Ireland)",
                US_EAST_2, "US East (Ohio)"
        );
    }
}
