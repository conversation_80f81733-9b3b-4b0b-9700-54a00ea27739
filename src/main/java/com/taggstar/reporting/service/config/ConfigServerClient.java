package com.taggstar.reporting.service.config;

import com.codahale.metrics.health.HealthCheck;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.taggstar.reporting.service.config.account.AccountConfig;
import com.taggstar.reporting.service.exceptions.ConfigNotFoundException;
import com.taggstar.reporting.service.exceptions.ConfigServerException;
import com.taggstar.reporting.service.exceptions.ConfigValidationException;
import io.dropwizard.lifecycle.Managed;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;

import static com.google.common.base.Strings.isNullOrEmpty;
import static java.lang.String.format;

/**
 * The facade for communicating with config server.
 */
public class ConfigServerClient extends HealthCheck implements Managed {

    private static final Logger log = LoggerFactory.getLogger(ConfigServerClient.class);

    private final String configServiceHost;

    private final int configServicePort;
    private final int configServiceAdminPort;

    private final Client client;

    private final ObjectMapper objectMapper;

    public ConfigServerClient(String configServiceHost, int configServicePort, int configServiceAdminPort, Client client, ObjectMapper objectMapper) {
        this.configServiceHost = configServiceHost;
        this.configServicePort = configServicePort;
        this.configServiceAdminPort = configServiceAdminPort;
        this.client = client;
        this.objectMapper = objectMapper;
    }

    public AccountConfig obtainAccountConfiguration(String accountId) {
        URI uri = accountConfigUri(accountId);
        log.atInfo().log("obtainAccountConfiguration: uri={}", uri);
        Response resp = client.target(uri).request().get();
        checkResponse(resp, format("Account config for account ID %s", accountId));
        try {
            JsonNode rootNode = objectMapper.readTree(resp.readEntity(String.class));
            JsonNode config = extractFromConfigDetail(rootNode, accountId);
            return objectMapper.treeToValue(config, AccountConfig.class);
        } catch (IOException e) {
            throw new ConfigServerException(
                    format("event=[account_config_error] detail=[Cannot un-marshall configuration into AccountConfig for account ID %s]",
                            accountId), e);
        }
    }

    public SiteConfig obtainSiteConfiguration(String siteKey) {
        return obtainConfiguration(siteKey, "site", SiteConfig.class);
    }

    public ExperimentReportConfig obtainExperimentReportConfig(String siteKey) {
        return obtainConfiguration(siteKey, "report", ExperimentReportConfig.class, "reportingService", "experimentReport");
    }

    public AssistantReportingConfig obtainAssistantReportConfig(String siteKey) {
        return obtainConfiguration(siteKey, "report", AssistantReportingConfig.class, "api", "assistantReporting");
    }

    /**
     * @param siteKey site key for which partition ID is returned
     * @return partition ID where the provided site belongs to
     */
    public String obtainPartitionId(String siteKey) {
        SiteConfig siteConf = obtainSiteConfiguration(siteKey);
        if (isNullOrEmpty(siteConf.getPartitionId())) {
            throw new ConfigValidationException(
                    format("event=[missing_partition_id] site_key=[%s] detail=[Partition ID is null or missing in site config]",
                            siteKey));
        }
        return siteConf.getPartitionId();
    }

    private <C> C obtainConfiguration(String siteKey, String type, Class<? extends C> pojoType, String... subPaths) {
        Response resp = client.target(siteConfigUri(siteKey, type)).request().get();
        checkResponse(resp, format("Configuration type %s for site key %s", type, siteKey));
        try {
            return objectMapper.treeToValue(readConfigObjectNode(resp, siteKey, subPaths), pojoType);
        } catch (IOException e) {
            throw new ConfigServerException(
                    format("event=[site_config_error] detail=[Cannot un-marshall configuration into %s for site key %s]",
                            pojoType.getSimpleName(), siteKey), e);
        }
    }

    private JsonNode readConfigObjectNode(Response resp, String siteKey, String... subPaths) {
        JsonNode rootNode;
        try {
            rootNode = objectMapper.readTree(resp.readEntity(String.class));
        } catch (IOException e) {
            throw new ConfigServerException(
                    format("event=[config_deserialization_error] detail=[Error when reading configuration object for siteKey %s]",
                            siteKey), e);
        }

        JsonNode config = extractFromConfigDetail(rootNode, siteKey);

        for (String subPath : subPaths) {
            config = config.get(subPath);
            if (config == null) {
                throw new ConfigValidationException(format(
                        "event=[config_deserialization_error] detail=[Field %s not found in config for siteKey %s]",
                        subPath, siteKey));
            }
        }

        return config;
    }

    private JsonNode extractFromConfigDetail(JsonNode rootNode, String id) {
        JsonNode config = rootNode.get("configDetail");
        if (config == null) {
            throw new ConfigValidationException(format(
                    "event=[config_deserialization_error] detail=[Field configDetail not found in response for %s]",
                    id));
        }

        config = config.get("config");
        if (config == null) {
            throw new ConfigValidationException(format(
                    "event=[config_deserialization_error] detail=[Field config not found in response for %s]",
                    id));
        }

        return config;
    }

    private URI siteConfigUri(String siteKey, String configType) {
        return UriBuilder
                .fromUri("api/v2/site/{siteKey}/config/{type}")
                .scheme("http")
                .host(configServiceHost)
                .port(configServicePort)
                .build(siteKey, configType);
    }

    private URI accountConfigUri(String accountId) {
        return UriBuilder
                .fromUri("api/v2/account/{accountNumber}")
                .scheme("http")
                .host(configServiceHost)
                .port(configServicePort)
                .build(accountId);
    }

    private void checkResponse(Response resp, String requestDesc) {
        if (resp.getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
            log.atError().log("event=[config_not_found] request=[{}] response=[{}] uri=[{}]",
                    requestDesc, resp.readEntity(String.class), resp.getLocation());
            throw new ConfigNotFoundException(
                    format("event=[config_not_found] detail=[%s]", requestDesc));
        }
        if (resp.getStatus() != Response.Status.OK.getStatusCode()) {
            throw new ConfigServerException(
                    format("event=[config_server_error] detail=[%s not obtain due to an unexpected response code %s]",
                            requestDesc, resp.getStatus()));
        }
    }

    @Override
    protected Result check() {
        Response resp =
                client.target(UriBuilder.fromUri("healthcheck").scheme("http").host(configServiceHost).port(configServiceAdminPort).build()).request().get();
        return (resp.getStatus() == Response.Status.OK.getStatusCode())
                ? Result.healthy(resp.readEntity(String.class).replace("\"", "'"))
                : Result.unhealthy("Unexpected response from config server: " + resp.getStatus());
    }

    @Override
    public void stop() {
        client.close();
        log.info("event=[config_service_client_stopped");
    }
}
