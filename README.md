# Reporting Service

A web service that provides comprehensive reporting and analytics from the PostgreSQL reporting database.

## Overview

- **Purpose**: Provides reporting and analytics services for experiments, messaging, conversions, and data validation
- **Primary Functions**:
    - Experiment reporting and analysis
    - Message coverage reporting
    - Time series analytics
    - Data validation
    - Module-specific analytics (product, category, basket, conversion)

## Service Configuration

### Ports

| Environment | Resource Port | Admin Port |
|------------|---------------|------------|
| PROD       | 8000          | 8001       |
| DEV        | 8000          | 8101       |
| QA         | 8100          | 8101       |

### Core Dependencies

- PostgreSQL (reporting database)
- AWS CloudWatch (monitoring)

## API Endpoints

### V3 Resources

Base path: `/api/v3/site/{sitekey}`

#### Experiment Endpoints
- `/experiment/report` - Detailed experiment reports
- `/experiment/report/series` - Time series experiment data
- `/experiments` - Experiment listings
- `/experiments/reports` - Stored experiment reports

### V4 Resources

Base path: `/api/v4/site/{sitekey}`

#### Module-Specific Endpoints
- **Assistant Module**
    - `/module/assistant/impressions/series`
    - `/module/assistant/report`

- **Basket Module**
    - `/module/basket/impressions/messages/report`
    - `/module/basket/impressions/messages/report/experience`
    - `/module/basket/impressions/series`

- **Category Module**
    - `/module/category/impressions/messages/report`
    - `/module/category/impressions/series`

- **Product Module**
    - `/module/product/impressions/messages/report`
    - `/module/product/impressions/messages/pairs/report`
    - `/module/product/impressions/series`

#### Validation Endpoints
- `/report/validation` - Data validation reports

#### Analytics Endpoints
- `/modules/requests/series` - Combined modules analytics
- `/tags/conversion/series` - Conversion tracking

## Development

### Requirements

- Java 22
- PostgreSQL
- Access to AWS services

### SQL Debugging

Set the `org.jooq` logger to DEBUG level in the configuration file for detailed SQL logging.

### Testing

Test scripts are located in `scripts/testing/`.

## Architecture

### Data Partitioning

- Each site belongs to a single database partition
- Partitions contain separate database access information
- Service determines correct database based on site information

## Links to Additional Documentation

### Related Documents

- [Reporting Terminology](https://docs.google.com/a/taggstar.com/document/d/1pp1d0JCQ-_-mFFKIGbgu9Du7beAHfMUs49dghFMMSY0) in Engineering/Specifications/Reporting
- [Application configuration](https://docs.google.com/spreadsheets/d/12k1CiJBuD1-xgV9GsORNaZVl80ew66QkXmHS6urfmsM) in Engineering folder
- [Operation manual](https://docs.google.com/document/d/1Qbv5BWJMaLBXUqTdpURMwnRw452P67MauGkQ7ZaxPN0) in Engineering/Operations folder
- [AWS instances](https://docs.google.com/spreadsheets/d/12YM6ug0Hai9AvPl3GQjqdKeYkSouCfH87MeCOKxLgiw/edit#gid=1908796722) in Engineering/Operations folder
- [Experiment Design Guide](https://docs.google.com/document/d/16OwXG4KMlFP0idQ-6iXeXSlaBFwVxJ20FCmwF9u3WAA) in Product Management/Experiments folder

### Developer Notes
- Ensure empty intervals are handled correctly