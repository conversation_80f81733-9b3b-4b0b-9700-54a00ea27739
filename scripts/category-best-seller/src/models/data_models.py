"""Data models for the application."""
from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Any, Optional

@dataclass
class ProductSalesData:
    """Product sales data from Athena."""
    product_id: str
    quantity_sold: int

@dataclass
class ProductDetail:
    """Product detail from product service."""
    id: str
    title: str
    brand: str
    url: str
    price: float
    category: str

@dataclass
class CategoryProduct:
    """Product with category information."""
    product_id: str
    title: str
    brand: str
    url: str
    price: float
    quantity_sold: int
    category: str
    rank: int

@dataclass
class FeedProduct:
    """Product in the final feed format."""
    product_id: str
    category: str
    category_rank: int
    time_window: str

@dataclass
class FeedMetadata:
    """Metadata for the category best sellers feed."""
    data_feed_name: str
    site_key: str
    category_depth: str
    created: str
    valid_until: str
    analysis_period_days: int
    total_products: int
    total_categories: int
    generated_at: str
    version: str = "1.0"

@dataclass
class CategoryBestSellersFeed:
    """Complete category best sellers feed with metadata."""
    metadata: FeedMetadata
    products: List[FeedProduct]