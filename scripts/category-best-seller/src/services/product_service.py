"""Product service for fetching product details."""
import requests
from typing import List, Dict
import logging

from ..core.exceptions import ProductServiceError
from ..models.data_models import ProductDetail

class ProductService:
    """Service for interacting with the product API."""
    
    def __init__(self, product_service_url: str, timeout: int = 30):
        self.product_service_url = product_service_url
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)
    
    def get_product_details(self, product_ids: List[str]) -> Dict[str, ProductDetail]:
        """Get product details from the product service."""
        if not product_ids:
            return {}
        
        payload = {"queryType": "product", "ids": product_ids}
        
        try:
            self.logger.info("Fetching details for %d products", len(product_ids))
            response = requests.post(self.product_service_url, json=payload, timeout=self.timeout)
            response.raise_for_status()
            
            response_data = response.json()
            
            if "products" not in response_data:
                raise ProductServiceError("Invalid response format: missing 'products' field")
            
            product_details = {}
            for product_data in response_data["products"]:
                product = ProductDetail(
                    id=product_data.get("id", ""),
                    title=product_data.get("title", ""),
                    brand=product_data.get("brand", ""),
                    url=product_data.get("url", ""),
                    price=float(product_data.get("price", 0)),
                    category=product_data.get("category", "Unknown")
                )
                product_details[product.id] = product
            
            self.logger.info("Successfully retrieved details for %d products", len(product_details))
            return product_details
            
        except requests.exceptions.RequestException as e:
            self.logger.error("HTTP error fetching product details: %s", e)
            raise ProductServiceError(f"Failed to fetch product details: {e}") from e
        except (KeyError, ValueError, TypeError) as e:
            self.logger.error("Error parsing product service response: %s", e)
            raise ProductServiceError(f"Invalid response from product service: {e}") from e
