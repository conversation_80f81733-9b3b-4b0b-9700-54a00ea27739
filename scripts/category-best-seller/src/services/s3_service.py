"""S3 service for file operations."""
import json
import logging
from typing import Any

from ..core.exceptions import AWSServiceError

class S3Service:
    """Service for interacting with AWS S3."""
    
    def __init__(self, s3_client, bucket_name: str):
        self.s3_client = s3_client
        self.bucket_name = bucket_name
        self.logger = logging.getLogger(__name__)
    
    def upload_json(self, key: str, data: Any) -> None:
        """Upload JSON data to S3."""
        try:
            json_data = json.dumps(data, indent=2)
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=key,
                Body=json_data,
                ContentType="application/json"
            )
            self.logger.info("Successfully uploaded JSON to s3://%s/%s", self.bucket_name, key)
        except Exception as e:
            self.logger.error("Failed to upload to S3: %s", e)
            raise AWSServiceError(f"S3 upload failed: {e}") from e
