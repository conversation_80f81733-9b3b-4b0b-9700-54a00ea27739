"""Athena service for running queries."""
import time
import pandas as pd
from datetime import datetime, timedelta
from typing import List
import boto3
import logging

from ..core.exceptions import AWSServiceError
from ..models.data_models import ProductSalesData

class AthenaService:
    """Service for interacting with AWS Athena."""
    
    def __init__(self, athena_client, s3_client, database: str, s3_output_bucket: str):
        self.athena_client = athena_client
        self.s3_client = s3_client
        self.database = database
        self.s3_output_bucket = s3_output_bucket
        self.logger = logging.getLogger(__name__)
    
    def get_product_sales_data(self, site_key: str, days_back: int = 1) -> List[ProductSalesData]:
        """Get product sales data from Athena."""
        cutoff_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
        
        query = f"""
        SELECT
            orderitem.id as product_id,
            SUM(orderitem.quantity) as quantity_sold
        FROM conversion_module
        CROSS JOIN UNNEST("order".orderitems) AS t(orderitem)
        WHERE sitekey = '{site_key}'
        AND dt > '{cutoff_date}'
        GROUP BY orderitem.id
        ORDER BY SUM(orderitem.quantity) DESC
        """
        
        self.logger.info("Getting sales data for site_key=%s, days_back=%s", site_key, days_back)
        df = self._execute_query(query)
        
        return [
            ProductSalesData(
                product_id=str(row["product_id"]),
                quantity_sold=int(row["quantity_sold"])
            )
            for _, row in df.iterrows()
        ]
    
    def _execute_query(self, query: str, max_wait_time: int = 300) -> pd.DataFrame:
        """Execute Athena query and return results as DataFrame."""
        try:
            self.logger.debug("Executing Athena query: %s...", query[:100])
            
            response = self.athena_client.start_query_execution(
                QueryString=query,
                QueryExecutionContext={"Database": self.database},
                ResultConfiguration={
                    "OutputLocation": f"s3://{self.s3_output_bucket}/athena_results/",
                    "EncryptionConfiguration": {"EncryptionOption": "SSE_S3"},
                },
            )
            
            query_execution_id = response["QueryExecutionId"]
            self.logger.info("Athena query started: %s", query_execution_id)
            
            # Wait for completion
            self._wait_for_query_completion(query_execution_id, max_wait_time)
            
            # Get results
            results = self.athena_client.get_query_results(QueryExecutionId=query_execution_id)
            
            # Convert to DataFrame
            columns = [col["Name"] for col in results["ResultSet"]["ResultSetMetadata"]["ColumnInfo"]]
            rows = []
            
            for row in results["ResultSet"]["Rows"][1:]:  # Skip header row
                row_data = [field.get("VarCharValue", "") for field in row["Data"]]
                rows.append(row_data)
            
            df = pd.DataFrame(rows, columns=columns)
            self.logger.info("Query completed successfully, retrieved %d rows", len(df))
            return df
            
        except Exception as e:
            self.logger.error("Athena query failed: %s", e)
            raise AWSServiceError(f"Failed to execute Athena query: {e}") from e
    
    def _wait_for_query_completion(self, query_execution_id: str, max_wait_time: int):
        """Wait for Athena query to complete."""
        start_time = time.time()
        
        while True:
            if time.time() - start_time > max_wait_time:
                raise AWSServiceError(f"Athena query timed out after {max_wait_time} seconds")
            
            query_status = self.athena_client.get_query_execution(
                QueryExecutionId=query_execution_id
            )["QueryExecution"]["Status"]
            
            status = query_status["State"]
            self.logger.debug("Query status: %s", status)
            
            if status == "SUCCEEDED":
                break
            elif status == "FAILED":
                error_reason = query_status.get('StateChangeReason', 'Unknown error')
                raise AWSServiceError(f"Athena query failed: {error_reason}") from None
            elif status == "CANCELLED":
                raise AWSServiceError("Athena query was cancelled")
            
            time.sleep(5)