"""Email service for sending fallback notifications via AWS SES."""
import logging
import boto3
from typing import Dict, Any, Optional, List
from botocore.exceptions import ClientError, NoCredentialsError

from ..core.exceptions import AWSServiceError


class EmailService:
    """Service for sending email notifications via AWS SES."""
    
    def __init__(self, region_name: str = "eu-west-1"):
        """Initialize the email service."""
        self.region_name = region_name
        self.logger = logging.getLogger(__name__)
        
        try:
            self.ses_client = boto3.client('ses', region_name=region_name)
        except NoCredentialsError:
            self.logger.error("AWS credentials not found for SES service")
            raise AWSServiceError("AWS credentials not found for SES service")
        except Exception as e:
            self.logger.error("Failed to initialize SES client: %s", e)
            raise AWSServiceError(f"Failed to initialize SES client: {e}")
    
    def send_webhook_failure_notification(
        self,
        from_email: str,
        to_emails: List[str],
        site_key: str,
        webhook_url: str,
        s3_bucket: str,
        s3_key: str,
        error_details: Optional[str] = None
    ) -> bool:
        """Send notification email when webhook fails."""
        subject = f"Webhook Failure Alert - Category Best Seller Feed for {site_key}"
        
        body = self._create_webhook_failure_email_body(
            site_key, webhook_url, s3_bucket, s3_key, error_details
        )
        
        return self._send_email(from_email, to_emails, subject, body)
    
    def _create_webhook_failure_email_body(
        self,
        site_key: str,
        webhook_url: str,
        s3_bucket: str,
        s3_key: str,
        error_details: Optional[str] = None
    ) -> str:
        """Create the email body for webhook failure notification."""
        body = f"""
Dear PS/Engineering Team,

This is an automated notification regarding a webhook delivery failure for the Category Best Seller feed.

**Feed Details:**
- Site Key: {site_key}
- S3 Location: s3://{s3_bucket}/{s3_key}
- Webhook URL: {webhook_url}

**Issue:**
The webhook notification to the customer endpoint failed after multiple retry attempts. The feed has been successfully uploaded to S3, but the customer has not been notified.

**Action Required:**
Please investigate the webhook endpoint and ensure the customer is notified of the feed availability.

**Error Details:**
{error_details or "Webhook failed after maximum retry attempts"}

**Next Steps:**
1. Verify the webhook URL is correct and accessible
2. Check if the customer's endpoint is operational
3. Manually notify the customer if necessary
4. Review webhook configuration for future runs

This is an automated message. Please do not reply to this email.

Best regards
        """
        
        return body.strip()
    
    def _send_email(
        self,
        from_email: str,
        to_emails: List[str],
        subject: str,
        body: str
    ) -> bool:
        """Send email via AWS SES."""
        try:
            response = self.ses_client.send_email(
                Source=from_email,
                Destination={
                    'ToAddresses': to_emails
                },
                Message={
                    'Subject': {
                        'Data': subject,
                        'Charset': 'UTF-8'
                    },
                    'Body': {
                        'Text': {
                            'Data': body,
                            'Charset': 'UTF-8'
                        }
                    }
                }
            )
            
            self.logger.info(
                "Email sent successfully to %s (Message ID: %s)",
                to_emails,
                response['MessageId']
            )
            return True
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            
            self.logger.error(
                "Failed to send email via SES (Error: %s): %s",
                error_code,
                error_message
            )
            
            if error_code == 'MessageRejected':
                self.logger.error("Email rejected - check sender verification and recipient permissions")
            elif error_code == 'MailFromDomainNotVerified':
                self.logger.error("Sender domain not verified in SES")
            elif error_code == 'ConfigurationSetDoesNotExist':
                self.logger.error("SES configuration set does not exist")
            
            return False
            
        except Exception as e:
            self.logger.error("Unexpected error sending email via SES: %s", e)
            return False
    
    def verify_email_identity(self, email: str) -> bool:
        """Verify an email address with SES. """
        try:
            self.ses_client.verify_email_identity(EmailAddress=email)
            self.logger.info("Verification email sent to %s", email)
            return True
        except ClientError as e:
            self.logger.error("Failed to send verification email to %s: %s", email, e)
            return False
