"""Webhook service for notifying customers of feed availability."""
import logging
import time
import requests
from typing import Dict, Any, Optional

from ..types import WebhookConfig


class WebhookService:
    """Service for sending webhook notifications to customer endpoints."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def notify_feed_ready(
        self,
        webhook_url: str,
        webhook_config: WebhookConfig,
        s3_bucket: str,
        s3_key: str,
        site_key: str,
        feed_metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Notify customer that feed is ready via webhook."""
        payload = {
            "event": "feed_ready",
            "site_key": site_key,
            "s3_location": {
                "bucket": s3_bucket,
                "key": s3_key,
                "url": f"s3://{s3_bucket}/{s3_key}"
            },
            "timestamp": int(time.time()),
            "metadata": feed_metadata or {}
        }
        
        # Create internal config with URL
        internal_config = WebhookConfig(
            timeout=webhook_config.timeout,
            max_retries=webhook_config.max_retries,
            retry_delay=webhook_config.retry_delay
        )
        return self._send_webhook_with_retry(webhook_url, internal_config, payload)
    
    def _send_webhook_with_retry(
        self,
        webhook_url: str,
        webhook_config: WebhookConfig,
        payload: Dict[str, Any]
    ) -> bool:
        """Send webhook with exponential backoff retry logic."""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Taggstar-CategoryBestSeller/1.0"
        }
        
        for attempt in range(webhook_config.max_retries + 1):
            try:
                self.logger.info(
                    "Sending webhook to %s (attempt %d/%d)",
                    webhook_url,
                    attempt + 1,
                    webhook_config.max_retries + 1
                )
                
                response = requests.post(
                    webhook_url,
                    json=payload,
                    headers=headers,
                    timeout=webhook_config.timeout
                )
                
                # Check if the request was successful
                if 200 <= response.status_code < 300:
                    self.logger.info(
                        "Webhook sent successfully to %s (status: %d)",
                        webhook_url,
                        response.status_code
                    )
                    return True
                else:
                    self.logger.warning(
                        "Webhook request failed with status %d: %s",
                        response.status_code,
                        response.text
                    )
                    
            except requests.exceptions.Timeout:
                self.logger.warning(
                    "Webhook request timed out (attempt %d/%d)",
                    attempt + 1,
                    webhook_config.max_retries + 1
                )
            except requests.exceptions.ConnectionError as e:
                self.logger.warning(
                    "Webhook connection error (attempt %d/%d): %s",
                    attempt + 1,
                    webhook_config.max_retries + 1,
                    e
                )
            except requests.exceptions.RequestException as e:
                self.logger.warning(
                    "Webhook request error (attempt %d/%d): %s",
                    attempt + 1,
                    webhook_config.max_retries + 1,
                    e
                )
            except Exception as e:
                self.logger.error(
                    "Unexpected error sending webhook (attempt %d/%d): %s",
                    attempt + 1,
                    webhook_config.max_retries + 1,
                    e
                )
            
            # If this wasn't the last attempt, wait before retrying
            if attempt < webhook_config.max_retries:
                delay = webhook_config.retry_delay * (2 ** attempt)  # Exponential backoff
                self.logger.info("Waiting %d seconds before retry", delay)
                time.sleep(delay)
        
        self.logger.error(
            "Failed to send webhook to %s after %d attempts",
            webhook_url,
            webhook_config.max_retries + 1
        )
        return False
