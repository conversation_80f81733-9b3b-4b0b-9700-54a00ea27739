"""Configuration service for fetching site configuration."""
import os
import requests
import logging
from typing import Dict, Any, Optional
from ..core.exceptions import ConfigurationError

logger = logging.getLogger(__name__)

class ConfigService:
    """Service for fetching site configuration from config endpoints."""

    CONFIG_ENDPOINTS = {
        "qa": {
            "eu": os.getenv("CONFIG_SERVICE_EU_ENDPOINT", "http://qaconfig01.taggstar.net:8000"),
            "us": os.getenv("CONFIG_SERVICE_US_ENDPOINT", "http://qaconfig01.taggstar.net:8000"),
        },
        "prod": {
            "eu": os.getenv("CONFIG_SERVICE_EU_ENDPOINT", "http://prdconfig01.taggstar.net:8080"),
            "us": os.getenv("CONFIG_SERVICE_US_ENDPOINT", "http://172.16.13.122:8080"),
        },
    }
    
    def __init__(self, timeout: int = 30):
        """Initialize the config service.
        
        Args:
            timeout: Request timeout in seconds
        """
        self.timeout = timeout
    
    def _get_region_type(self, aws_region: str) -> str:
        """Determine if the AWS region is EU or US based."""
        if aws_region == 'eu-west-1':
            return "eu"
        elif aws_region == 'us-east-2':
            return "us"
        else:
            logger.error("Unknown region format '%s', defaulting to EU config service", aws_region)
            raise ConfigurationError(f"Unknown region format '{aws_region}'")
    
    def get_site_config(self, site_key: str, aws_region: str, environment: str) -> Dict[str, Any]:
        """Fetch site configuration from the appropriate config service."""
        region_type = self._get_region_type(aws_region)
        endpoint = self.CONFIG_ENDPOINTS[environment][region_type]
        
        url = f"{endpoint}/api/v2/site/{site_key}/config/site"
        logger.info("Fetching config for site '%s' from %s config service: %s", site_key, region_type.upper(), url)
        
        try:
            response = requests.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            site_config_json = response.json()
            site_config = site_config_json["configDetail"]["config"]
            
            if "cbsFeedConfig" not in site_config:
                raise ConfigurationError(f"Config response missing 'cbsFeedConfig' for site '{site_key}'")
            
            logger.info("Successfully fetched config for site '%s' from %s config service", site_key, region_type.upper())
            return site_config
            
        except requests.exceptions.RequestException as e:
            logger.error("Failed to fetch config from %s config service: %s", region_type.upper(), e)
            raise ConfigurationError(f"Failed to fetch config from {region_type.upper()} config service: {e}") from e
        except ValueError as e:
            logger.error("Invalid JSON response from %s config service: %s", region_type.upper(), e)
            raise ConfigurationError(f"Invalid JSON response from {region_type.upper()} config service: {e}") from e
        except Exception as e:
            logger.error("Unexpected error fetching config from %s config service (%s): %s", environment, region_type.upper(), e)
            raise ConfigurationError(f"Unexpected error fetching config from {environment} config service ({region_type.upper()}): {e}") from e
