"""Feed generation utilities."""
from datetime import datetime, timedelta
from typing import Dict, List
import logging

from ..models.data_models import (
    CategoryProduct, CategoryBestSellersFeed, FeedProduct, FeedMetadata,
    ProductSalesData, ProductDetail
)
from .category_processor import CategoryProcessor

class FeedGenerator:
    """Generator for category best sellers feed."""
    
    def __init__(self, category_processor: CategoryProcessor):
        self.category_processor = category_processor
        self.logger = logging.getLogger(__name__)
    
    def generate_category_products(
        self,
        sales_data: List[ProductSalesData],
        product_details: Dict[str, ProductDetail],
        max_products_per_category: int = 10,
        max_categories: int | None = None
    ) -> Dict[str, List[CategoryProduct]]:
        """Generate category products from sales data and product details."""
        category_data: Dict[str, List[CategoryProduct]] = {}
        
        for sales_item in sales_data:
            if sales_item.product_id not in product_details:
                self.logger.debug("Product %s not found in product details", sales_item.product_id)
                continue
            
            product = product_details[sales_item.product_id]
            normalized_category = self.category_processor.normalize_category(product.category)
            
            if normalized_category not in category_data:
                category_data[normalized_category] = []
            
            category_product = CategoryProduct(
                product_id=product.id,
                title=product.title,
                brand=product.brand,
                url=product.url,
                price=product.price,
                quantity_sold=sales_item.quantity_sold,
                category=normalized_category,
                rank=0  # Will be set later
            )
            
            category_data[normalized_category].append(category_product)
        
        # Sort and rank products within each category
        category_bestsellers: Dict[str, List[CategoryProduct]] = {}
        sorted_categories = sorted(category_data.items(), key=lambda x: sum(p.quantity_sold for p in x[1]), reverse=True)
        
        # Apply category limit if specified
        if max_categories is not None:
            sorted_categories = sorted_categories[:max_categories]
        
        for category, products in sorted_categories:
            sorted_products = sorted(products, key=lambda x: x.quantity_sold, reverse=True)
            
            # Assign ranks and limit to specified number
            for i, product in enumerate(sorted_products[:max_products_per_category]):
                product.rank = i + 1
            
            category_bestsellers[category] = sorted_products[:max_products_per_category]
        
        self.logger.info("Generated best sellers for %d categories", len(category_bestsellers))
        return category_bestsellers
    
    def create_feed(
        self,
        category_bestsellers: Dict[str, List[CategoryProduct]],
        site_key: str,
        category_depth: int,
        days_back: int = 1
    ) -> CategoryBestSellersFeed:
        """Create the final JSON feed."""
        now = datetime.now()
        valid_until = now + timedelta(days=1)
        
        # Format dates
        created_str = now.strftime("%A, %d-%b-%Y %H:%M:%S GMT+0000")
        valid_until_str = valid_until.strftime("%A, %d-%b-%Y %H:%M:%S GMT+0000")
        
        # Create feed products
        feed_products = []
        for category, products in category_bestsellers.items():
            for product in products:
                feed_product = FeedProduct(
                    product_id=product.product_id,
                    category=f"/{category}",
                    category_rank=product.rank,
                    time_window=f"{days_back}D"
                )
                feed_products.append(feed_product)
        
        # Create metadata
        metadata = FeedMetadata(
            data_feed_name="Category Best Seller",
            site_key=site_key,
            category_depth=str(category_depth),
            created=created_str,
            valid_until=valid_until_str,
            analysis_period_days=days_back,
            total_products=len(feed_products),
            total_categories=len(category_bestsellers),
            generated_at=now.isoformat()
        )
        
        feed = CategoryBestSellersFeed(
            metadata=metadata,
            products=feed_products
        )
        
        self.logger.info("Created feed with %d products", len(feed_products))
        return feed
