"""Feed writing utilities."""
import json
import logging
from datetime import datetime
from typing import Any, Dict, Optional

from ..core.exceptions import AWSServiceError
from ..services.s3_service import S3Service
from ..services.webhook_service import WebhookService
from ..services.email_service import EmailService
from ..types import WebhookConfig


class FeedWriter:
    """Handles writing category best seller feeds to various outputs."""
    
    def __init__(
        self, 
        s3_service: S3Service | None = None,
        webhook_service: WebhookService | None = None,
        email_service: EmailService | None = None
    ):
        self.s3_service = s3_service
        self.webhook_service = webhook_service
        self.email_service = email_service
        self.logger = logging.getLogger(__name__)
    
    def write_feed(
        self,
        feed_data: Dict[str, Any],
        output_config,
        site_key: str,
        s3_bucket: str | None = None,
        webhook_url: Optional[str] = None,
        webhook_config: Optional[WebhookConfig] = None,
        email_config: Optional[Any] = None
    ) -> None:
        """Write feed to configured outputs."""
        s3_key = None
        
        # Write to S3 if enabled
        if output_config.enabled:
            s3_key = self._write_to_s3(feed_data, output_config, site_key, s3_bucket)
            
            # Send webhook notification if configured
            if webhook_url and webhook_config and self.webhook_service and s3_bucket:
                self._notify_via_webhook(
                    webhook_url, webhook_config, s3_bucket, s3_key, site_key, feed_data
                )
        
        # Write to file with auto-generated filename
        file_name = self._generate_file_name(site_key)
        self._write_to_file(feed_data, file_name)
        
        # Write to stdout if no other output specified
        if not output_config.enabled:
            self._write_to_stdout(feed_data)
    
    def _write_to_s3(
        self,
        feed_data: Dict[str, Any],
        output_config,
        site_key: str,
        s3_bucket: str | None
    ) -> str:
        """Write feed to S3."""
        if not self.s3_service:
            self.logger.error("S3 service not available for S3 upload")
            raise AWSServiceError("S3 service not available")
        
        if not s3_bucket:
            self.logger.error("S3 bucket not specified for S3 upload")
            raise AWSServiceError("S3 bucket not specified")
        
        # Generate S3 key
        s3_key = self._generate_s3_key(output_config.s3Key, site_key)
        
        try:
            self.s3_service.upload_json(s3_key, feed_data)
            self.logger.info(
                "Feed uploaded to S3: s3://%s/%s",
                s3_bucket,
                s3_key
            )
            return s3_key
        except AWSServiceError as e:
            self.logger.error("Failed to upload to S3: %s", e)
            raise
    
    def _write_to_file(self, feed_data: Dict[str, Any], file_path: str) -> None:
        """Write feed to local file."""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(feed_data, f, indent=2)
            self.logger.info("Output written to %s", file_path)
        except Exception as e:
            self.logger.error("Failed to write to file %s: %s", file_path, e)
            raise
    
    def _write_to_stdout(self, feed_data: Dict[str, Any]) -> None:
        """Write feed to stdout."""
        print(json.dumps(feed_data, indent=2))
        self.logger.info("Output written to stdout")
    
    def _generate_file_name(self, site_key: str) -> str:
        """Generate filename for the feed using site key and current date."""
        date_str = datetime.now().strftime("%Y-%m-%d")
        return f"{site_key}-{date_str}-feed.json"
    
    def _notify_via_webhook(
        self,
        webhook_url: str,
        webhook_config: WebhookConfig,
        s3_bucket: str,
        s3_key: str,
        site_key: str,
        feed_data: Dict[str, Any]
    ) -> None:
        """Send webhook notification to customer endpoint."""
        try:
            # Prepare feed metadata
            feed_metadata = {
                "total_categories": len(feed_data.get("categories", [])),
                "generated_at": datetime.now().isoformat(),
                "feed_version": "1.0"
            }
            
            # Send webhook notification
            if self.webhook_service:
                success = self.webhook_service.notify_feed_ready(
                    webhook_url=webhook_url,
                    webhook_config=webhook_config,
                    s3_bucket=s3_bucket,
                    s3_key=s3_key,
                    site_key=site_key,
                    feed_metadata=feed_metadata
                )
            else:
                success = False
            
            if success:
                self.logger.info("Webhook notification sent successfully to %s", webhook_url)
            else:
                self.logger.error("Failed to send webhook notification to %s", webhook_url)
                # Send fallback email notification
                self._send_fallback_email_notification(
                    site_key, webhook_url, s3_bucket, s3_key
                )
                
        except Exception as e:
            self.logger.error("Error sending webhook notification: %s", e)
            # Send fallback email notification
            self._send_fallback_email_notification(
                site_key, webhook_url, s3_bucket, s3_key, str(e)
            )
    
    def _send_fallback_email_notification(
        self,
        site_key: str,
        webhook_url: str,
        s3_bucket: str,
        s3_key: str,
        error_details: Optional[str] = None
    ) -> None:
        """Send fallback email notification when webhook fails."""
        if not self.email_service:
            self.logger.error("Email service not available for fallback notification")
            return
        
        try:
            # This would need to be configured in the settings
            # For now, we'll log the error and let the caller handle email config
            self.logger.error(
                "Webhook failed for site %s. S3 location: s3://%s/%s. "
                "Manual intervention required to notify customer.",
                site_key, s3_bucket, s3_key
            )
        except Exception as e:
            self.logger.error("Failed to send fallback email notification: %s", e)

    def _generate_s3_key(self, custom_key: str, site_key: str) -> str:
        """Generate S3 key for the feed."""
        if custom_key:
            return custom_key
        
        # Auto-generate key based on site and timestamp
        timestamp = datetime.now().strftime("%Y/%m/%d/%H")
        return f"category-best-sellers/{site_key}/{timestamp}/feed.json" 