"""Category processing utilities."""
from typing import List

class CategoryProcessor:
    """Processor for normalizing and handling categories."""
    
    def __init__(self, category_separators: List[str] = None, category_depth: int = 3):
        self.category_separators = category_separators or ["/", ">", "|", "\\"]
        self.category_depth = category_depth
    
    def normalize_category(self, category: str) -> str:
        """Normalize category to specified depth and clean format."""
        if not category or category == "Unknown":
            return "Unknown"
        
        # Find which separator is used
        separator_used = "/"
        for sep in self.category_separators:
            if sep in category:
                separator_used = sep
                break
        
        # Split, clean, and limit depth
        parts = [part.strip() for part in category.split(separator_used) if part.strip()]
        
        if len(parts) > self.category_depth and self.category_depth > 0:
            parts = parts[:self.category_depth]
        
        return "/".join(parts) if parts else "Unknown"
