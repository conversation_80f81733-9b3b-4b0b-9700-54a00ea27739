"""Custom exceptions for the application."""

class CategoryBestSellerError(Exception):
    """Base exception for the Category Best Seller application."""
    pass

class ConfigurationError(CategoryBestSellerError):
    """Raised when there's a configuration error."""
    pass

class AWSServiceError(CategoryBestSellerError):
    """Raised when AWS service operations fail."""
    pass

class ProductServiceError(CategoryBestSellerError):
    """Raised when product service operations fail."""
    pass

class DataProcessingError(CategoryBestSellerError):
    """Raised when data processing fails."""
    pass