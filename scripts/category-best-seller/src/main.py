"""Main application entry point."""
import sys
import argparse
from dataclasses import asdict
import traceback
import boto3

from .config.settings import Settings
from .core.logging import LoggerFactory
from .core.exceptions import CategoryBestSellerError, ConfigurationError
from .services.athena_service import AthenaService
from .services.product_service import ProductService
from .services.s3_service import S3Service
from .services.webhook_service import WebhookService
from .services.email_service import EmailService
from .processors.category_processor import CategoryProcessor
from .processors.feed_generator import FeedGenerator
from .processors.feed_writer import FeedWriter

class CategoryBestSellerApp:
    """Main application class."""
    
    def __init__(self, site_key: str, region: str, environment: str):
        self.site_key = site_key
        self.region = region
        self.settings = Settings.from_config_service(site_key, region, environment)
        
        self.logger = LoggerFactory.create_logger(
            name="category_bestseller",
            level=self.settings.local.logging.level,
            log_file=self.settings.local.logging.file,
            log_format=self.settings.local.logging.format
        )
        
        self._setup_services()
    
    def _setup_services(self):
        """Initialize AWS and other services."""
        try:
            # Initialize AWS services
            athena_client = boto3.client('athena', region_name=self.region)
            s3_client = boto3.client('s3', region_name=self.region)
            
            self.athena_service = AthenaService(
                athena_client=athena_client,
                s3_client=s3_client,
                database=self.settings.local.athena.database,
                s3_output_bucket=self.settings.local.athena_output_bucket
            )
            
            # Get product service URL based on partition
            if self.settings.partition_id not in self.settings.local.product_service.partition_endpoints:
                raise ConfigurationError(f"Partition {self.settings.partition_id} not found in partitionEndpoints")
            product_service_url = self.settings.local.product_service.partition_endpoints[self.settings.partition_id]
            product_service_url = f"{product_service_url}/api/v1/key/{self.site_key}/products/query?locale={self.settings.local.locale}"
            
            self.product_service = ProductService(
                product_service_url=product_service_url,
                timeout=self.settings.local.product_service.timeout
            )
            
            if self.settings.local.output.enabled:
                self.s3_service = S3Service(s3_client, self.settings.local.output.s3_bucket)
            else:
                self.s3_service = None
            
            # Initialize webhook and email services
            self.webhook_service = WebhookService()
            self.email_service = EmailService(region_name=self.settings.local.email.region)
            
            self.category_processor = CategoryProcessor(
                category_separators=self.settings.category_separators,
                category_depth=self.settings.category_depth
            )
            
            self.feed_generator = FeedGenerator(self.category_processor)
            self.feed_writer = FeedWriter(
                s3_service=self.s3_service,
                webhook_service=self.webhook_service,
                email_service=self.email_service
            )
            
            self.logger.info("Successfully initialized all services")
            
        except Exception as e:
            self.logger.error("Failed to initialize services: %s", e)
            raise ConfigurationError(f"Service initialization failed: {e}") from e
    
    def run(self):
        """Run the main application logic."""
        try:
            self.logger.info("Starting Category Best Seller Generator")
            self.logger.info(
                "Configuration: siteKey=%s, daysBack=%s, categoryDepth=%s",
                self.site_key,
                self.settings.days_back,
                self.settings.category_depth
            )
            
            self.logger.info("Step 1: Fetching product sales data")
            sales_data = self.athena_service.get_product_sales_data(
                site_key=self.site_key,
                days_back=self.settings.days_back
            )
            self.logger.info("Found %d products with sales data", len(sales_data))
            
            if not sales_data:
                self.logger.warning("No sales data found, exiting")
                return
            
            self.logger.info("Step 2: Fetching product details")
            product_ids = [item.product_id for item in sales_data]
            product_details = self.product_service.get_product_details(product_ids=product_ids)
            self.logger.info("Retrieved details for %d products", len(product_details))
            
            self.logger.info("Step 3: Generating category best sellers")
            category_bestsellers = self.feed_generator.generate_category_products(
                sales_data=sales_data,
                product_details=product_details,
                max_products_per_category=self.settings.max_products_per_category,
                max_categories=self.settings.max_categories
            )
            self.logger.info("Generated best sellers for %d categories", len(category_bestsellers))
            
            self.logger.info("Step 4: Creating feed")
            feed_data = self.feed_generator.create_feed(
                category_bestsellers=category_bestsellers,
                site_key=self.site_key,
                category_depth=self.settings.category_depth,
                days_back=self.settings.days_back
            )
            
            self.logger.info("Step 5: Writing output")
            feed_dict = asdict(feed_data)
            self.feed_writer.write_feed(
                feed_data=feed_dict,
                output_config=self.settings.local.output,
                site_key=self.site_key,
                s3_bucket=self.settings.local.output.s3_bucket,
                webhook_url=self.settings.webhook_url,
                webhook_config=self.settings.local.webhook,
                email_config=self.settings.local.email
            )
            
            self.logger.info("Category Best Seller feed generation completed successfully")
            
        except Exception as e:
            self.logger.error("Application error: %s", e)
            raise

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Category Best Seller Generator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m src.main
  python -m src.main -s hugobosscom -r eu-west-1
  python -m src.main --siteKey argoscouk --region eu-west-1
        """
    )
    parser.add_argument(
        "-s", "--siteKey",
        required=True,
        help="Site key"
    )
    parser.add_argument(
        "-r", "--region",
        required=True,
        help="AWS region (eu-west-1, us-east-2)"
    )
    parser.add_argument(
        "-e", "--environment",
        required=True,
        help="Environment (qa, prod)"
    )
    return parser.parse_args()

def main():
    """Main entry point."""
    args = parse_arguments()
    
    try:
        app = CategoryBestSellerApp(site_key=args.siteKey, region=args.region, environment=args.environment)
        app.run()
    except CategoryBestSellerError as e:
        print(f"Application error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()
