"""Type definitions to avoid circular imports."""
from dataclasses import dataclass
from typing import List


@dataclass
class WebhookConfig:
    """Webhook configuration for local settings."""
    timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 5


@dataclass
class EmailConfig:
    """Email configuration for fallback notifications."""
    from_email: str
    to_emails: List[str]
    region: str = "eu-west-1"
