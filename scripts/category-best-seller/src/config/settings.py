"""Configuration management."""
import os
import json
from dataclasses import dataclass, field
from pathlib import Path
from typing import Optional, Dict, Any, List

from ..services.config_service import ConfigService
from ..core.exceptions import ConfigurationError
from ..types import WebhookConfig, EmailConfig

@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    file: Optional[str] = None
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

@dataclass
class OutputConfig:
    """Output configuration."""
    s3_bucket: str = ""
    s3_key: Optional[str] = None
    enabled: bool = False

@dataclass
class AthenaConfig:
    """Athena configuration."""
    database: str
    max_wait_time: int = 300

@dataclass
class ProductServiceConfig:
    """Product service configuration."""
    partition_endpoints: Dict[str, str]
    timeout: int = 30



@dataclass
class LocalConfig:
    """Local configuration that should not be fetched from config service."""
    locale: str
    athena: AthenaConfig
    product_service: ProductServiceConfig
    output: OutputConfig
    athena_output_bucket: str
    logging: LoggingConfig
    webhook: WebhookConfig
    email: EmailConfig

@dataclass
class Settings:
    """Main settings class."""
    local: LocalConfig
    days_back: int
    category_depth: int
    max_products_per_category: int
    partition_id: str
    max_categories: int | None = None
    category_separators: List[str] = field(default_factory=lambda: ["/", ">", "|", "\\"])
    webhook_url: Optional[str] = None
    
    @classmethod
    def from_config_service(cls, site_key: str, region: str, environment: str) -> "Settings":
        """Load settings from config service based on site key and region."""
        # Load local config first
        local_config = cls._load_local_config()
        
        # Load config from config service
        config_service = ConfigService()
        site_config = config_service.get_site_config(site_key, region, environment)
        
        config_data = site_config["cbsFeedConfig"]
        
        # Apply environment variable overrides
        config_data = cls._apply_env_overrides(config_data)
        
        # Validate required fields from config service
        required_fields = ["days_back", "category_depth", "max_products_per_category", "output"]
        for field in required_fields:
            if field not in config_data:
                raise ConfigurationError(f"Missing required configuration field: {field}")
        
        # Create settings instance
        settings = cls(
            local=local_config,
            days_back=config_data["days_back"],
            category_depth=config_data["category_depth"],
            max_products_per_category=config_data["max_products_per_category"],
            partition_id=config_data.get("partition_id", "01"),
            max_categories=config_data.get("max_categories"),
            category_separators=config_data.get("category_separators", ["/", ">", "|", "\\"]),
            webhook_url=config_data.get("output", {}).get("webhook_url")
        )
        
        settings.validate()
        return settings
    
    @classmethod
    def _load_local_config(cls) -> LocalConfig:
        """Load local configuration from local-config.json."""
        config_path = Path("local-config.json")
        
        if not config_path.exists():
            raise ConfigurationError("Local configuration file not found: local-config.json")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
        except json.JSONDecodeError as e:
            raise ConfigurationError(f"Invalid JSON in local configuration file: {e}") from e
        except Exception as e:
            raise ConfigurationError(f"Error reading local configuration file: {e}") from e
        
        # Apply environment variable overrides
        config_data = cls._apply_env_overrides(config_data)
        
        # Validate required sections
        required_sections = ["athena", "product_service", "logging"]
        for section in required_sections:
            if section not in config_data:
                raise ConfigurationError(f"Missing required local configuration section: {section}")
        
        return LocalConfig(
            locale=config_data.get("locale", "en-GB"),
            athena=AthenaConfig(**config_data["athena"]),
            product_service=ProductServiceConfig(**config_data["product_service"]),
            output=OutputConfig(**config_data.get("output", {})),
            athena_output_bucket=(config_data.get("athena_output_bucket", "")),
            logging=LoggingConfig(**config_data["logging"]),
            webhook=WebhookConfig(**config_data.get("webhook", {})),
            email=EmailConfig(**config_data.get("email", {}))
        )
    
    @staticmethod
    def _apply_env_overrides(config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration."""
        env_mappings = [
            # Local config mappings
            (("locale",), "LOCALE"),
            (("athena", "database"), "ATHENA_DATABASE"),
            (("athena", "max_wait_time"), "ATHENA_MAX_WAIT_TIME"),
            (("product_service", "timeout"), "PRODUCT_SERVICE_TIMEOUT"),
            (("output", "s3_bucket"), "OUTPUT_BUCKET"),
            (("output", "s3_key"), "OUTPUT_S3_KEY"),
            (("output", "enabled"), "OUTPUT_ENABLED"),
            (("athena_output", "bucket"), "ATHENA_OUTPUT_BUCKET"),
            (("logging", "level"), "LOG_LEVEL"),
            (("logging", "file"), "LOG_FILE"),
            (("webhook", "timeout"), "WEBHOOK_TIMEOUT"),
            (("webhook", "max_retries"), "WEBHOOK_MAX_RETRIES"),
            (("webhook", "retry_delay"), "WEBHOOK_RETRY_DELAY"),
            (("email", "from_email"), "EMAIL_FROM"),
            (("email", "to_emails"), "EMAIL_TO"),
            (("email", "region"), "EMAIL_REGION"),
            # Config service mappings
            (("days_back",), "DAYS_BACK"),
            (("category_depth",), "CATEGORY_DEPTH"),
            (("max_products_per_category",), "MAX_PRODUCTS_PER_CATEGORY"),
            (("max_categories",), "MAX_CATEGORIES"),
        ]
        
        for key_path, env_var in env_mappings:
            env_value = os.getenv(env_var)
            if env_value is not None:
                if len(key_path) == 1:
                    # Single key (e.g., "locale")
                    config_data[key_path[0]] = env_value
                else:
                    # Nested key (e.g., "athena", "database")
                    section, key = key_path[0], key_path[1]
                    if section not in config_data:
                        config_data[section] = {}
                    config_data[section][key] = env_value
        
        return config_data
    
    def validate(self) -> None:
        """Validate configuration values."""
        if self.days_back <= 0:
            raise ConfigurationError("days_back must be positive")
        
        if self.category_depth <= 0:
            raise ConfigurationError("category_depth must be positive")
        
        if self.max_products_per_category <= 0:
            raise ConfigurationError("max_products_per_category must be positive")
        
        if self.max_categories is not None and self.max_categories <= 0:
            raise ConfigurationError("max_categories must be positive if specified")
        
        if self.local.athena.max_wait_time <= 0:
            raise ConfigurationError("max_wait_time must be positive")
        
        if self.local.output.enabled and not self.local.output.s3_bucket:
            raise ConfigurationError("S3 bucket must be specified when S3 output is enabled")
        
