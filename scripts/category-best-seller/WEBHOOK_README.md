# Webhook Notification System

This document explains how the webhook notification system works in the Category Best Seller service.

## Overview

When a feed JSON is successfully uploaded to S3, the system will automatically notify customers via webhook. If the webhook fails after multiple retry attempts, a fallback email notification is sent to the support team.

## Configuration

### Config Service (Customer Webhook URL)

The webhook URL is configured in the config service and comes from the customer. It should be added to the `cbsFeedConfig.output.webhook_url` field in the config service.

Example config service structure:
```json
{
  "cbsFeedConfig": {
    "output": {
      "webhook_url": "https://customer-endpoint.com/webhooks/feed-ready"
    }
  }
}
```

### Local Configuration (Technical Settings)

The webhook technical settings are configured in the local `local-config.json` file:

```json
{
  "webhook": {
    "timeout": 30,
    "max_retries": 3,
    "retry_delay": 5
  },
  "email": {
    "from_email": "<EMAIL>",
    "to_emails": ["<EMAIL>", "<EMAIL>"],
    "region": "eu-west-1"
  }
}
```

## Webhook Payload

When a feed is ready, the system sends a POST request to the customer's webhook URL with the following JSON payload:

```json
{
  "event": "feed_ready",
  "site_key": "example-site",
  "s3_location": {
    "bucket": "your-s3-bucket",
    "key": "category-best-sellers/example-site/2024/01/15/14/feed.json",
    "url": "s3://your-s3-bucket/category-best-sellers/example-site/2024/01/15/14/feed.json"
  },
  "timestamp": 1705324800,
  "metadata": {
    "total_categories": 25,
    "generated_at": "2024-01-15T14:00:00",
    "feed_version": "1.0"
  }
}
```

## Retry Logic

The webhook system uses exponential backoff for retries:

- **Initial delay**: 5 seconds
- **Retry delays**: 5s, 10s, 20s (exponential backoff)
- **Maximum retries**: 3 attempts
- **Timeout**: 30 seconds per request

## Fallback Email Notification

If all webhook attempts fail, the system sends an email notification to the configured support team. The email includes:

- Site key
- S3 location of the uploaded feed
- Webhook URL that failed
- Error details
- Action items for manual intervention

## Environment Variables

You can override webhook and email settings using environment variables:

```bash
# Webhook settings
export WEBHOOK_TIMEOUT=45
export WEBHOOK_MAX_RETRIES=5
export WEBHOOK_RETRY_DELAY=10

# Email settings
export EMAIL_FROM="<EMAIL>"
export EMAIL_TO="<EMAIL>,<EMAIL>"
export EMAIL_REGION="us-east-1"
```

## AWS SES Setup

For email notifications to work, you need to:

1. **Verify sender email**: The `from_email` must be verified in AWS SES
2. **Verify recipient emails**: All `to_emails` must be verified (unless in production mode)
3. **IAM permissions**: The service needs SES permissions to send emails

Required IAM permissions:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ses:SendEmail",
        "ses:SendRawEmail"
      ],
      "Resource": "*"
    }
  ]
}
```

## Testing

To test the webhook system:

1. Set up a test webhook endpoint (e.g., using webhook.site)
2. Configure the webhook URL in your test environment
3. Run the category best seller service
4. Verify the webhook payload is received correctly

## Troubleshooting

### Common Issues

1. **Webhook timeout**: Increase the `timeout` setting
2. **Too many retries**: Adjust `max_retries` and `retry_delay`
3. **Email not sent**: Check SES configuration and IAM permissions
4. **Webhook URL not found**: Verify the URL is configured in the config service

### Logs

The system logs webhook activities with the following log levels:
- `INFO`: Successful webhook delivery
- `WARNING`: Retry attempts and temporary failures
- `ERROR`: Final failure and fallback email notifications

### Manual Intervention

If webhooks consistently fail, the support team should:
1. Verify the customer's webhook endpoint is operational
2. Check network connectivity to the webhook URL
3. Manually notify the customer of feed availability
4. Review and update webhook configuration if needed
