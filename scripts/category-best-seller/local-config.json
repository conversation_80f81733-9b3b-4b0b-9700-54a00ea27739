{"locale": "en-GB", "athena": {"database": "taggstar_reporting", "max_wait_time": 300}, "product_service": {"timeout": 30, "partition_endpoints": {"01": "http://172.31.6.252:9100", "02": "http://172.31.39.59:9100", "03": "http://172.31.26.202:9100", "04": "http://172.31.22.82:9100", "05": "http://172.31.28.170:9100"}}, "athena_output_bucket": "aws-athena-query-results-682109183784-eu-west-1", "output": {"s3_bucket": ""}, "logging": {"level": "INFO", "file": "category-best-seller.log", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "webhook": {"timeout": 30, "max_retries": 3, "retry_delay": 5}, "email": {"from_email": "<EMAIL>", "to_emails": ["<EMAIL>"], "region": "eu-west-1"}}