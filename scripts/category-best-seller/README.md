# Category Best Seller Feed

A Python application that generates category best seller feeds by analyzing product order data from AWS Athena and enriching it with product details from Product Service.

## What it does

This application:
1. Fetches orders data from AWS Athena `conversion_module` table for a specific site and time period
2. Retrieves detailed product information from Product Service
3. Processes categories and generates best seller rankings
4. Outputs a JSON feed with category best sellers

## Setup

1. Install dependencies using `uv` (recommended):
```bash
uv sync
```

Or using pip:
```bash
pip install -r requirements.txt
```

2. Configure the application by editing `config.json`:
   - Set your AWS credentials and region
   - Configure the site key and time period
   - Set the product service URL

## Usage

Run the application with site key, AWS region, and environment:
```bash
python -m src.main -s argoscouk -r eu-west-1 -e prod
```

Or with long-form arguments:
```bash
python -m src.main --siteKey hugobosscom --region us-east-1 --environment qa
```

The application will automatically:
1. Load local configuration from `local-config.json`
2. Determine the appropriate config service (EU or US) based on the AWS region and environment
3. Fetch the site configuration from the config service
4. Extract the `cbsFeedConfig` object from the response
5. Combine local and remote configuration to run the application

### Config Service Integration

The application now integrates with a config service that provides site-specific configuration:

- **EU Config Service**: Used for regions starting with `eu-`, `ap-`, `me-`, or `af-`
- **US Config Service**: Used for regions starting with `us-` (defaults to US for other regions)

You can configure the config service endpoints using environment variables:
```bash
export CONFIG_SERVICE_EU_ENDPOINT="https://your-eu-config-service.com/api/v1/sites"
export CONFIG_SERVICE_US_ENDPOINT="https://your-us-config-service.com/api/v1/sites"
```

### Legacy Configuration (Optional)

If you prefer to use a local config file instead of the config service:
```bash
python -m src.main -c my-config.json
```

### Configuration Structure

The application now uses a hybrid configuration approach:

#### Local Configuration (`local-config.json`)
Contains configuration that should be local to the Python module:
```json
{
  "locale": "en-GB",
  "athena": {
    "database": "taggstar_reporting",
    "max_wait_time": 300
  },
  "product_service": {
    "timeout": 30,
    "partition_endpoints": {
      "01": "http://172.31.6.252:9100",
      "02": "http://172.31.39.59:9100",
      "03": "http://172.31.26.202:9100",
      "04": "http://172.31.22.82:9100",
      "05": "http://172.31.28.170:9100"
    }
  },
  "athena_output": {
    "bucket": "aws-athena-query-results-682109183784-eu-west-1"
  },
  "logging": {
    "level": "INFO",
    "file": null,
    "format": "%(asctime)s - %(name)s - %(message)s"
  }
}
```

**Note**: Output filenames are automatically generated using the format `{site_key}-{yyyy-mm-dd}-feed.json` (e.g., `argoscouk-2024-01-15-feed.json`).

#### Config Service Response Format

The config service should return a response with the following structure:
```json
{
  "cbsFeedConfig": {
    "days_back": 7,
    "category_depth": 5,
    "max_products_per_category": 10,
    "max_categories": null,
    "output": {
      "enabled": false,
      "s3_bucket": "aws-athena-query-results-682109183784-eu-west-1"
    }
  }
}
```

The `cbsFeedConfig` object contains only site-specific configuration, while local configuration is kept in `local-config.json`.

**Note**: Product service configuration (partition endpoints and timeout) is now part of the local configuration and not fetched from the config service.

### Output Options

The application supports multiple output methods:

1. **Local file** (default): Saves the JSON feed to a local file with auto-generated filename
2. **Stdout**: Prints the JSON feed to console when S3 output is enabled
3. **S3 upload**: Enable `output.enabled: true` to upload to S3 bucket



Example S3 configuration in config service:
```json
{
  "output": {
    "enabled": true,
    "s3_bucket": "my-feed-bucket"
  }
}
```

If `s3_key` is not provided, it will auto-generate a key based on site and timestamp.

### Output Structure

The feed includes comprehensive metadata:

```json
{
  "metadata": {
    "data_feed_name": "Category Best Seller",
    "site_key": "hugobosscom",
    "category_depth": "3",
    "created": "Monday, 29-Jul-2024 18:00:10 GMT+0000",
    "valid_until": "Tuesday, 30-Jul-2024 18:00:10 GMT+0000",
    "analysis_period_days": 1,
    "total_products": 25,
    "total_categories": 15,
    "generated_at": "2024-07-29T18:00:10.123456",
    "version": "1.0"
  },
  "products": [
    {
      "product_id": "hbeu50531224",
      "category": "/Men/Clothing/Sweats",
      "category_rank": 1,
      "time_window": "1D"
    }
  ]
}
```

## Configuration

The main configuration options in `config.json`:
- `site_key`: The site to analyze (e.g., "hugobosscom")
- `partition`: The partition number (01-05) for the site
- `days_back`: Number of days to look back for sales data
- `category_depth`: How deep to process category hierarchies
- `max_products_per_category`: Maximum products per category (default: 10)
- `max_categories`: Maximum number of categories to include (default: null = all categories)
- AWS settings:
  - `s3_output_bucket`: For Athena query results (temporary files)
  - `s3_feed_bucket`: For category best seller feeds (optional, defaults to s3_output_bucket)
- Product service configuration:
  - `partition_endpoints`: Partition-specific endpoints (01-05)
- Output settings:
  - `output.file`: Local file path to save the feed (optional)
  - `output.s3_enabled`: Enable S3 upload (default: false)
  - `output.s3_key`: Custom S3 key path (optional, auto-generated if not provided)

Environment variables can override config values (see [`src/config/settings.py`](src/config/settings.py) for mappings).
