import datetime
import requests


def req(end_date):
    return requests.request('GET',
                            'http://prdreporting01.taggstar.net:8000/api/v3/site/argoscouk/experiment/report?deviceType'
                            '=desktop&deviceType=mobile&deviceType=tablet&experimentId=experiment9&startDate=2019-05-01'
                            '&endDate=' + end_date)


start = datetime.datetime(2019, 5, 1)
date_list = [(start + datetime.timedelta(days=x)).isoformat()[:10] for x in range(0, 13)]

print('date,uplift')
for date in date_list:
    data = req(date).json()
    overall = [cat for cat in data if cat['category'] == 'Overall'][0]
    print(date + ", " + str(overall['uplift']['actualPercentageUplift'] * 100))
