import argparse
import json
import pathlib
import shutil
from datetime import datetime
from enum import Enum
from typing import Optional, Dict

from compare_reports import ReportComparator
from fetch_all_experiments_reports import ExperimentReporter, Region


class BaselineStatus(str, Enum):
    MISSING = "missing"
    EXISTS = "exists"
    OUTDATED = "outdated"

class RegressionTestManager:
    def __init__(self, base_dir: str = "regression_tests", refresh_cache: bool = False):
        self.base_dir = pathlib.Path(base_dir)
        self.refresh_cache = refresh_cache
        self.baseline_dir = self.base_dir / "baseline"
        self.results_dir = self.base_dir / "results"
        self.setup_directories()

    def setup_directories(self):
        """Create necessary directory structure."""
        self.baseline_dir.mkdir(parents=True, exist_ok=True)
        self.results_dir.mkdir(parents=True, exist_ok=True)

    def get_baseline_status(self, region: Region) -> BaselineStatus:
        """Check if baseline exists and is fresh for given region."""
        baseline_path = self.baseline_dir / region.value
        if not baseline_path.exists():
            return BaselineStatus.MISSING

        # Check if baseline is older than 24 hours
        manifest_path = baseline_path / "manifest.json"
        if not manifest_path.exists():
            return BaselineStatus.OUTDATED

        try:
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            baseline_time = datetime.fromisoformat(manifest['timestamp'])
            age = datetime.now() - baseline_time

            if age.days >= 1:
                return BaselineStatus.OUTDATED
            return BaselineStatus.EXISTS
        except (json.JSONDecodeError, KeyError, ValueError):
            return BaselineStatus.OUTDATED

    def generate_baseline(self, region: Region, force: bool = False) -> bool:
        """Generate baseline for given region if needed or forced."""
        status = self.get_baseline_status(region)

        if status == BaselineStatus.EXISTS and not force:
            print(f"Baseline for {region.value} exists and is fresh. Use --force to regenerate.")
            return False

        print(f"Generating baseline for {region.value}...")
        reporter = ExperimentReporter(region=region,refresh_cache=self.refresh_cache)
        reporter.process_site_experiments()

        # Move generated reports to baseline directory
        if reporter.output_dir.exists():
            baseline_path = self.baseline_dir / region.value
            if baseline_path.exists():
                shutil.rmtree(baseline_path)
            shutil.copytree(reporter.output_dir, baseline_path)
            print(f"Baseline generated at: {baseline_path}")
            return True

        return False

    def run_comparison(self, region: Region) -> Optional[Dict]:
        """Run tests and compare with baseline."""
        baseline_path = self.baseline_dir / region.value
        if not baseline_path.exists():
            print(f"No baseline found for {region.value}. Please generate baseline first.")
            return None

        # Generate new test results
        print(f"Generating new test results for {region.value}...")
        reporter = ExperimentReporter(region=region, refresh_cache=self.refresh_cache)
        reporter.process_site_experiments()

        # Compare results
        print("Comparing results with baseline...")
        comparator = ReportComparator(str(baseline_path), str(reporter.output_dir))
        result = comparator.compare_reports()

        # Save test results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_path = self.results_dir / region.value / timestamp
        if reporter.output_dir.exists():
            results_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copytree(reporter.output_dir, results_path)
            print(f"Test results saved at: {results_path}")

        return result

    def compare_directories(self, dir1: str, dir2: str) -> Optional[Dict]:
        """Compare two existing report directories."""
        dir1_path = pathlib.Path(dir1)
        dir2_path = pathlib.Path(dir2)

        if not dir1_path.exists():
            print(f"Directory not found: {dir1}")
            return None

        if not dir2_path.exists():
            print(f"Directory not found: {dir2}")
            return None

        print(f"Comparing directories:\n  {dir1}\n  {dir2}")
        comparator = ReportComparator(str(dir1_path), str(dir2_path))
        return comparator.compare_reports()

    def rebaseline(self, region: Region):
        """Update baseline with specific test results."""
        results_path = self.results_dir / region.value
        if not results_path.exists():
            print(f"Results not found at: {results_path}")
            return False

        baseline_path = self.baseline_dir / region.value
        if baseline_path.exists():
            shutil.rmtree(baseline_path)

        shutil.copytree(results_path, baseline_path)
        print(f"Baseline updated with results from: {results_path}")
        return True

def main():
    parser = argparse.ArgumentParser(description='Manage regression tests for experiment reports')
    parser.add_argument(
        '--region',
        type=str,
        choices=[r.value for r in Region],
        default=Region.EU.value,
        help='Region to test (eu, us, eu-local, us-local)'
    )

    subparsers = parser.add_subparsers(dest='command', help='Command to execute')

    # Baseline command
    baseline_parser = subparsers.add_parser('baseline', help='Generate baseline')
    baseline_parser.add_argument(
        '--force',
        action='store_true',
        help='Force baseline regeneration even if recent baseline exists'
    )

    # Test command
    subparsers.add_parser('test', help='Run tests and compare with baseline')

    # Compare command
    compare_parser = subparsers.add_parser('compare', help='Compare two existing report directories')
    compare_parser.add_argument('dir1', help='First directory path')
    compare_parser.add_argument('dir2', help='Second directory path')

    # Rebaseline command
    subparsers.add_parser('rebaseline', help='Update baseline with specific test results')

    parser.add_argument(
        '--refreshCache',
        action='store_true',
        help='Refresh the cache for the reports'
    )

    args = parser.parse_args()
    refresh_cache = args.refreshCache
    manager = RegressionTestManager(refresh_cache=refresh_cache)

    if args.command == 'baseline':
        manager.generate_baseline(Region(args.region), args.force)
    elif args.command == 'test':
        manager.run_comparison(Region(args.region))
    elif args.command == 'compare':
        manager.compare_directories(args.dir1, args.dir2)
    elif args.command == 'rebaseline':
        manager.rebaseline(Region(args.region))
    else:
        parser.print_help()

if __name__ == "__main__":
    main()