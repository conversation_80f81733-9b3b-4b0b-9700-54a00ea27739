[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2019-07-09", "experimentId": "tvc2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "jaca<PERSON><PERSON><PERSON>", "startDate": "2017-04-25"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1092472, "name": "Taggstar Experiment Sessions", "total": 21372796, "treatment": 20280324, "type": "long"}, {"adjusted": null, "control": 0.0511, "name": "Experiment split", "total": null, "treatment": 0.9489, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3628185, "name": "Taggstar Experiment Impressions", "total": 71926906, "treatment": 68298721, "type": "long"}, {"adjusted": null, "control": 0.0504, "name": "Experiment split", "total": null, "treatment": 0.9496, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 54424717, "treatment": 54424717, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.7969, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 2196904, "control": 109246, "name": "Number of orders", "total": 2193855, "treatment": 2084609, "type": "long"}, {"adjusted": 59648, "control": null, "name": "Uplift", "total": null, "treatment": 56599, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.1028, "control": 0.1, "name": "Conversion rate", "total": 0.1014, "treatment": 0.1028, "type": "percentage"}, {"adjusted": 0.0279, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0279, "type": "percentage"}, {"adjusted": null, "control": 0.0994, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.2021, "treatment": 0.1027, "type": "percentage"}, {"adjusted": null, "control": 0.1006, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.2035, "treatment": 0.1029, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0208, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0351, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 163408006, "control": 8162663, "name": "Revenue", "total": 163218058, "treatment": 155055394, "type": "currency"}, {"adjusted": null, "control": 74.72, "name": "AOV", "total": 74.4, "treatment": 74.38, "type": "currency"}, {"adjusted": 3716101, "control": null, "name": "Uplift", "total": null, "treatment": 3526152, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0279, "actualQuantityUplift": 56598.88, "adjustedQuantityUplift": 59647.78, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.4583827308082584e-21}}]