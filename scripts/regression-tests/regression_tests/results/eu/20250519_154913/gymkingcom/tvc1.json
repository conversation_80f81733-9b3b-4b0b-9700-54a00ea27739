[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2021-10-12", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "gymkingcom", "startDate": "2021-09-03"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 301812, "name": "Taggstar Experiment Sessions", "total": 602702, "treatment": 300890, "type": "long"}, {"adjusted": null, "control": 0.5008, "name": "Experiment split", "total": null, "treatment": 0.4992, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2165149, "name": "Taggstar Experiment Impressions", "total": 4311742, "treatment": 2146593, "type": "long"}, {"adjusted": null, "control": 0.5022, "name": "Experiment split", "total": null, "treatment": 0.4978, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 1968480, "treatment": 1968480, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.917, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 23396, "control": 11295, "name": "Number of orders", "total": 22975, "treatment": 11680, "type": "long"}, {"adjusted": 840, "control": null, "name": "Uplift", "total": null, "treatment": 420, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0388, "control": 0.0374, "name": "Conversion rate", "total": 0.0381, "treatment": 0.0388, "type": "percentage"}, {"adjusted": 0.0373, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0373, "type": "percentage"}, {"adjusted": null, "control": 0.0367, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0749, "treatment": 0.0381, "type": "percentage"}, {"adjusted": null, "control": 0.0381, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0776, "treatment": 0.0395, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0007, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0752, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 4577490, "control": 1376763, "name": "Revenue", "total": 3662007, "treatment": 2285244, "type": "currency"}, {"adjusted": null, "control": 121.89, "name": "AOV", "total": 159.39, "treatment": 195.65, "type": "currency"}, {"adjusted": 1828169, "control": null, "name": "Uplift", "total": null, "treatment": 912686, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0373, "actualQuantityUplift": 419.5, "adjustedQuantityUplift": 840.3, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0023551197771730496}}]