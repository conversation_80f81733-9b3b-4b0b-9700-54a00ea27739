[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-05-25", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-05-20"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 13234, "name": "Taggstar Experiment Sessions", "total": 26357, "treatment": 13123, "type": "long"}, {"adjusted": null, "control": 0.5021, "name": "Experiment split", "total": null, "treatment": 0.4979, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 60496, "name": "Taggstar Experiment Impressions", "total": 117365, "treatment": 56869, "type": "long"}, {"adjusted": null, "control": 0.5155, "name": "Experiment split", "total": null, "treatment": 0.4845, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 14328, "treatment": 14328, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.2519, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 1157, "control": 609, "name": "Number of orders", "total": 1185, "treatment": 576, "type": "long"}, {"adjusted": -56, "control": null, "name": "Uplift", "total": null, "treatment": -28, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0439, "control": 0.046, "name": "Conversion rate", "total": 0.045, "treatment": 0.0439, "type": "percentage"}, {"adjusted": -0.0462, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0462, "type": "percentage"}, {"adjusted": null, "control": 0.0424, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0828, "treatment": 0.0404, "type": "percentage"}, {"adjusted": null, "control": 0.0496, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.097, "treatment": 0.0474, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.1855, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.1166, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 140130, "control": 74692, "name": "Revenue", "total": 144461, "treatment": 69770, "type": "currency"}, {"adjusted": null, "control": 122.65, "name": "AOV", "total": 121.91, "treatment": 121.13, "type": "currency"}, {"adjusted": -8627, "control": null, "name": "Uplift", "total": null, "treatment": -4295, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0462, "actualQuantityUplift": -27.89, "adjustedQuantityUplift": -56.02, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7974915555663769}}]