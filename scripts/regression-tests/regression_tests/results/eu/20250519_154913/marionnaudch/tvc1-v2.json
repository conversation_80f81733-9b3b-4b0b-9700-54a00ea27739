[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-06-26", "experimentId": "tvc1-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-05-26"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 65580, "name": "Taggstar Experiment Sessions", "total": 131130, "treatment": 65550, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 272745, "name": "Taggstar Experiment Impressions", "total": 543281, "treatment": 270536, "type": "long"}, {"adjusted": null, "control": 0.502, "name": "Experiment split", "total": null, "treatment": 0.498, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 100704, "treatment": 100704, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.3722, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 5581, "control": 2717, "name": "Number of orders", "total": 5507, "treatment": 2790, "type": "long"}, {"adjusted": 149, "control": null, "name": "Uplift", "total": null, "treatment": 74, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0426, "control": 0.0414, "name": "Conversion rate", "total": 0.042, "treatment": 0.0426, "type": "percentage"}, {"adjusted": 0.0273, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0273, "type": "percentage"}, {"adjusted": null, "control": 0.0399, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0809, "treatment": 0.041, "type": "percentage"}, {"adjusted": null, "control": 0.043, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0871, "treatment": 0.0441, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0451, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.1053, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 731944, "control": 362909, "name": "Revenue", "total": 728797, "treatment": 365888, "type": "currency"}, {"adjusted": null, "control": 133.57, "name": "AOV", "total": 132.34, "treatment": 131.14, "type": "currency"}, {"adjusted": 6293, "control": null, "name": "Uplift", "total": null, "treatment": 3146, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0273, "actualQuantityUplift": 74.24, "adjustedQuantityUplift": 148.52, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.15329978533339084}}]