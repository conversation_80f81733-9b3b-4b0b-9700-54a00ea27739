[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2021-12-07", "experimentId": "experiment4", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "simplybecouk", "startDate": "2020-09-23"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1278401, "name": "Taggstar Experiment Sessions", "total": 25561740, "treatment": 24283339, "type": "long"}, {"adjusted": null, "control": 0.05, "name": "Experiment split", "total": null, "treatment": 0.95, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5354355, "name": "Taggstar Experiment Impressions", "total": 106282871, "treatment": 100928516, "type": "long"}, {"adjusted": null, "control": 0.0504, "name": "Experiment split", "total": null, "treatment": 0.9496, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 67053163, "treatment": 67053163, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.6644, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 2295863, "control": 112875, "name": "Number of orders", "total": 2293917, "treatment": 2181042, "type": "long"}, {"adjusted": 38918, "control": null, "name": "Uplift", "total": null, "treatment": 36971, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0898, "control": 0.0883, "name": "Conversion rate", "total": 0.0891, "treatment": 0.0898, "type": "percentage"}, {"adjusted": 0.0172, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0172, "type": "percentage"}, {"adjusted": null, "control": 0.0878, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1775, "treatment": 0.0897, "type": "percentage"}, {"adjusted": null, "control": 0.0888, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1787, "treatment": 0.0899, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0103, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0242, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 155211413, "control": 7657072, "name": "Revenue", "total": 155106008, "treatment": 147448936, "type": "currency"}, {"adjusted": null, "control": 67.84, "name": "AOV", "total": 67.62, "treatment": 67.6, "type": "currency"}, {"adjusted": 2107589, "control": null, "name": "Uplift", "total": null, "treatment": 2002183, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0172, "actualQuantityUplift": 36971.49, "adjustedQuantityUplift": 38917.86, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.6961753862666952e-09}}]