[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2016-06-21", "experimentId": "experiment1", "experimentState": "finished", "isSeries": "false", "metric": "impressions", "orderType": "conversions", "siteKey": "blackscouk", "startDate": "2016-05-04"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Experiment Sessions", "total": 0, "treatment": 0, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Experiment split", "total": null, "treatment": 0.0, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1091999, "name": "Taggstar Experiment Impressions", "total": 2178152, "treatment": 1086153, "type": "long"}, {"adjusted": null, "control": 0.5013, "name": "Experiment split", "total": null, "treatment": 0.4987, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 621110, "treatment": 621110, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.5718, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 27781, "control": 13469, "name": "Number of orders", "total": 27322, "treatment": 13853, "type": "long"}, {"adjusted": 915, "control": null, "name": "Uplift", "total": null, "treatment": 456, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0128, "control": 0.0123, "name": "Conversion rate", "total": 0.0125, "treatment": 0.0128, "type": "percentage"}, {"adjusted": 0.034, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.034, "type": "percentage"}, {"adjusted": null, "control": 0.0121, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0247, "treatment": 0.0125, "type": "percentage"}, {"adjusted": null, "control": 0.0125, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0255, "treatment": 0.013, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0002, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0691, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2203336, "control": 1035773, "name": "Revenue", "total": 2134484, "treatment": 1098711, "type": "currency"}, {"adjusted": null, "control": 76.9, "name": "AOV", "total": 78.12, "treatment": 79.31, "type": "currency"}, {"adjusted": 137336, "control": null, "name": "Uplift", "total": null, "treatment": 68484, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.034, "actualQuantityUplift": 456.11, "adjustedQuantityUplift": 914.67, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0026831361483098865}}]