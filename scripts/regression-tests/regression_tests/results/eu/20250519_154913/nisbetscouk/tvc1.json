[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-07-13", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-05-31"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1215148, "name": "Taggstar Experiment Sessions", "total": 2428864, "treatment": 1213716, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4117388, "name": "Taggstar Experiment Impressions", "total": 8234942, "treatment": 4117554, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 2340313, "treatment": 2340313, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.5684, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 93729, "control": 46181, "name": "Number of orders", "total": 93018, "treatment": 46837, "type": "long"}, {"adjusted": 1422, "control": null, "name": "Uplift", "total": null, "treatment": 710, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0386, "control": 0.038, "name": "Conversion rate", "total": 0.0383, "treatment": 0.0386, "type": "percentage"}, {"adjusted": 0.0154, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0154, "type": "percentage"}, {"adjusted": null, "control": 0.0377, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0759, "treatment": 0.0382, "type": "percentage"}, {"adjusted": null, "control": 0.0383, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0773, "treatment": 0.0389, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0025, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0337, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 23350418, "control": 11230083, "name": "Revenue", "total": 22898408, "treatment": 11668326, "type": "currency"}, {"adjusted": null, "control": 243.18, "name": "AOV", "total": 246.17, "treatment": 249.13, "type": "currency"}, {"adjusted": 903487, "control": null, "name": "Uplift", "total": null, "treatment": 451477, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0154, "actualQuantityUplift": 710.42, "adjustedQuantityUplift": 1421.68, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.00873506257190333}}]