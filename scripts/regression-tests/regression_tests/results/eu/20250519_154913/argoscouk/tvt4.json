[{"category": "Overall", "metaData": {"categoryEnabled": "true", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-10-18", "experimentId": "tvt4", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "argoscouk", "startDate": "2022-09-29"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 13763891, "name": "Taggstar Experiment Sessions", "total": 27515887, "treatment": 13751996, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 43280807, "name": "Taggstar Experiment Impressions", "total": 73472926, "treatment": 30192119, "type": "long"}, {"adjusted": null, "control": 0.5891, "name": "Experiment split", "total": null, "treatment": 0.4109, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 26959212, "name": "Taggstar Message Impressions", "total": 56187507, "treatment": 29228295, "type": "long"}, {"adjusted": null, "control": 0.6229, "name": "Taggstar coverage", "total": null, "treatment": 0.9681, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 1575759, "control": 789635, "name": "Number of orders", "total": 1577174, "treatment": 787539, "type": "long"}, {"adjusted": -2828, "control": null, "name": "Uplift", "total": null, "treatment": -1414, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0573, "control": 0.0574, "name": "Conversion rate", "total": 0.0573, "treatment": 0.0573, "type": "percentage"}, {"adjusted": -0.0018, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0018, "type": "percentage"}, {"adjusted": null, "control": 0.0572, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1144, "treatment": 0.0571, "type": "percentage"}, {"adjusted": null, "control": 0.0575, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1149, "treatment": 0.0574, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0061, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0025, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 137144819, "control": 68171143, "name": "Revenue", "total": 136713909, "treatment": 68542766, "type": "currency"}, {"adjusted": null, "control": 86.33, "name": "AOV", "total": 86.68, "treatment": 87.03, "type": "currency"}, {"adjusted": 861447, "control": null, "name": "Uplift", "total": null, "treatment": 430537, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0018, "actualQuantityUplift": -1413.58, "adjustedQuantityUplift": -2828.39, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8769370381656573}}]