[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-04-16", "experimentId": "tvt4", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "shoptottenhamhotspurcom", "startDate": "2025-01-06"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 7950923, "name": "Taggstar Experiment Sessions", "total": 15895024, "treatment": 7944101, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 10978815, "name": "Taggstar Experiment Impressions", "total": 21924274, "treatment": 10945459, "type": "long"}, {"adjusted": null, "control": 0.5008, "name": "Experiment split", "total": null, "treatment": 0.4992, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 7662222, "name": "Taggstar Message Impressions", "total": 17191762, "treatment": 9529540, "type": "long"}, {"adjusted": null, "control": 0.6979, "name": "Taggstar coverage", "total": null, "treatment": 0.8706, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 35599, "control": 18246, "name": "Number of orders", "total": 36038, "treatment": 17792, "type": "long"}, {"adjusted": -877, "control": null, "name": "Uplift", "total": null, "treatment": -438, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0022, "control": 0.0023, "name": "Conversion rate", "total": 0.0023, "treatment": 0.0022, "type": "percentage"}, {"adjusted": -0.024, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.024, "type": "percentage"}, {"adjusted": null, "control": 0.0023, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0045, "treatment": 0.0022, "type": "percentage"}, {"adjusted": null, "control": 0.0023, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0046, "treatment": 0.0023, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0521, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0048, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2324339, "control": 1171731, "name": "Revenue", "total": 2333402, "treatment": 1161671, "type": "currency"}, {"adjusted": null, "control": 64.22, "name": "AOV", "total": 64.75, "treatment": 65.29, "type": "currency"}, {"adjusted": -18117, "control": null, "name": "Uplift", "total": null, "treatment": -9055, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.024, "actualQuantityUplift": -438.34, "adjustedQuantityUplift": -877.07, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9896300965781574}}]