[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-04-18", "experimentId": "tvt9", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "ctshirtscom", "startDate": "2024-03-08"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2419374, "name": "Taggstar Experiment Sessions", "total": 4835270, "treatment": 2415896, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 14376835, "name": "Taggstar Experiment Impressions", "total": 28745514, "treatment": 14368679, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 13963695, "name": "Taggstar Message Impressions", "total": 27919564, "treatment": 13955869, "type": "long"}, {"adjusted": null, "control": 0.9713, "name": "Taggstar coverage", "total": null, "treatment": 0.9713, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 220671, "control": 110613, "name": "Number of orders", "total": 220869, "treatment": 110256, "type": "long"}, {"adjusted": -396, "control": null, "name": "Uplift", "total": null, "treatment": -198, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0456, "control": 0.0457, "name": "Conversion rate", "total": 0.0457, "treatment": 0.0456, "type": "percentage"}, {"adjusted": -0.0018, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0018, "type": "percentage"}, {"adjusted": null, "control": 0.0455, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0908, "treatment": 0.0454, "type": "percentage"}, {"adjusted": null, "control": 0.046, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0919, "treatment": 0.0459, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0132, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0098, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 35798092, "control": 17816712, "name": "Revenue", "total": 35702883, "treatment": 17886171, "type": "currency"}, {"adjusted": null, "control": 161.07, "name": "AOV", "total": 161.65, "treatment": 162.22, "type": "currency"}, {"adjusted": 190280, "control": null, "name": "Uplift", "total": null, "treatment": 95072, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0018, "actualQuantityUplift": -197.99, "adjustedQuantityUplift": -396.26, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.6669669918373828}}]