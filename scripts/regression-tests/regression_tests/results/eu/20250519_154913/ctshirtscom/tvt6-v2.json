[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-11-29", "experimentId": "tvt6-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "ctshirtscom", "startDate": "2022-11-22"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 562331, "name": "Taggstar Experiment Sessions", "total": 1124885, "treatment": 562554, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4953901, "name": "Taggstar Experiment Impressions", "total": 9935692, "treatment": 4981791, "type": "long"}, {"adjusted": null, "control": 0.4986, "name": "Experiment split", "total": null, "treatment": 0.5014, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 4821188, "name": "Taggstar Message Impressions", "total": 9642595, "treatment": 4821407, "type": "long"}, {"adjusted": null, "control": 0.9732, "name": "Taggstar coverage", "total": null, "treatment": 0.9678, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 86429, "control": 43146, "name": "Number of orders", "total": 86369, "treatment": 43223, "type": "long"}, {"adjusted": 120, "control": null, "name": "Uplift", "total": null, "treatment": 60, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0768, "control": 0.0767, "name": "Conversion rate", "total": 0.0768, "treatment": 0.0768, "type": "percentage"}, {"adjusted": 0.0014, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0014, "type": "percentage"}, {"adjusted": null, "control": 0.076, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1522, "treatment": 0.0761, "type": "percentage"}, {"adjusted": null, "control": 0.0774, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.155, "treatment": 0.0775, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0166, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0197, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 14170211, "control": 6978138, "name": "Revenue", "total": 14064648, "treatment": 7086510, "type": "currency"}, {"adjusted": null, "control": 161.73, "name": "AOV", "total": 162.84, "treatment": 163.95, "type": "currency"}, {"adjusted": 211166, "control": null, "name": "Uplift", "total": null, "treatment": 105604, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0014, "actualQuantityUplift": 59.89, "adjustedQuantityUplift": 119.76, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.4160344774691249}}]