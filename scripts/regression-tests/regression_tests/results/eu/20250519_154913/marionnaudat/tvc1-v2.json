[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-06-26", "experimentId": "tvc1-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-05-26"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 49061, "name": "Taggstar Experiment Sessions", "total": 97382, "treatment": 48321, "type": "long"}, {"adjusted": null, "control": 0.5038, "name": "Experiment split", "total": null, "treatment": 0.4962, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 210370, "name": "Taggstar Experiment Impressions", "total": 410409, "treatment": 200039, "type": "long"}, {"adjusted": null, "control": 0.5126, "name": "Experiment split", "total": null, "treatment": 0.4874, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 62055, "treatment": 62055, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.3102, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 3432, "control": 1759, "name": "Number of orders", "total": 3462, "treatment": 1703, "type": "long"}, {"adjusted": -59, "control": null, "name": "Uplift", "total": null, "treatment": -29, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0352, "control": 0.0359, "name": "Conversion rate", "total": 0.0355, "treatment": 0.0352, "type": "percentage"}, {"adjusted": -0.017, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.017, "type": "percentage"}, {"adjusted": null, "control": 0.0342, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0678, "treatment": 0.0336, "type": "percentage"}, {"adjusted": null, "control": 0.0375, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0744, "treatment": 0.0369, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.104, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0783, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 420135, "control": 215004, "name": "Revenue", "total": 423475, "treatment": 208471, "type": "currency"}, {"adjusted": null, "control": 122.23, "name": "AOV", "total": 122.32, "treatment": 122.41, "type": "currency"}, {"adjusted": -6631, "control": null, "name": "Uplift", "total": null, "treatment": -3290, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.017, "actualQuantityUplift": -29.47, "adjustedQuantityUplift": -59.39, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.6963410548802434}}]