[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-12-20", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "theperfumeshopcom", "startDate": "2022-11-29"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2171497, "name": "Taggstar Experiment Sessions", "total": 4342051, "treatment": 2170554, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 7493559, "name": "Taggstar Experiment Impressions", "total": 14979049, "treatment": 7485490, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 7359688, "name": "Taggstar Message Impressions", "total": 14714052, "treatment": 7354364, "type": "long"}, {"adjusted": null, "control": 0.9821, "name": "Taggstar coverage", "total": null, "treatment": 0.9825, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 224239, "control": 111348, "name": "Number of orders", "total": 223443, "treatment": 112095, "type": "long"}, {"adjusted": 1591, "control": null, "name": "Uplift", "total": null, "treatment": 795, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0516, "control": 0.0513, "name": "Conversion rate", "total": 0.0515, "treatment": 0.0516, "type": "percentage"}, {"adjusted": 0.0071, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0071, "type": "percentage"}, {"adjusted": null, "control": 0.051, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1023, "treatment": 0.0513, "type": "percentage"}, {"adjusted": null, "control": 0.0516, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1035, "treatment": 0.0519, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0043, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0187, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 15576713, "control": 7777000, "name": "Revenue", "total": 15563666, "treatment": 7786665, "type": "currency"}, {"adjusted": null, "control": 69.84, "name": "AOV", "total": 69.65, "treatment": 69.46, "type": "currency"}, {"adjusted": 26090, "control": null, "name": "Uplift", "total": null, "treatment": 13042, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0071, "actualQuantityUplift": 795.35, "adjustedQuantityUplift": 1591.05, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.04199415254112571}}]