[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-05-18", "experimentId": "tvt5-v2", "experimentState": "finished", "isSeries": "false", "metric": "impressions", "orderType": "conversions", "siteKey": "bootscom", "startDate": "2023-04-27"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 9940591, "name": "Taggstar Experiment Sessions", "total": 19887800, "treatment": 9947209, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 25543424, "name": "Taggstar Experiment Impressions", "total": 51131549, "treatment": 25588125, "type": "long"}, {"adjusted": null, "control": 0.4996, "name": "Experiment split", "total": null, "treatment": 0.5004, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 23319460, "name": "Taggstar Message Impressions", "total": 46683652, "treatment": 23364192, "type": "long"}, {"adjusted": null, "control": 0.9129, "name": "Taggstar coverage", "total": null, "treatment": 0.9131, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 655037, "control": 326613, "name": "Number of orders", "total": 654418, "treatment": 327805, "type": "long"}, {"adjusted": 1240, "control": null, "name": "Uplift", "total": null, "treatment": 620, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0128, "control": 0.0128, "name": "Conversion rate", "total": 0.0128, "treatment": 0.0128, "type": "percentage"}, {"adjusted": 0.0019, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0019, "type": "percentage"}, {"adjusted": null, "control": 0.0127, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0255, "treatment": 0.0128, "type": "percentage"}, {"adjusted": null, "control": 0.0128, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0257, "treatment": 0.0129, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0049, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0087, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 20843677, "control": 10418362, "name": "Revenue", "total": 20849311, "treatment": 10430950, "type": "currency"}, {"adjusted": null, "control": 31.9, "name": "AOV", "total": 31.86, "treatment": 31.82, "type": "currency"}, {"adjusted": -11279, "control": null, "name": "Uplift", "total": null, "treatment": -5644, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0019, "actualQuantityUplift": 620.43, "adjustedQuantityUplift": 1239.77, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.22028684136376014}}]