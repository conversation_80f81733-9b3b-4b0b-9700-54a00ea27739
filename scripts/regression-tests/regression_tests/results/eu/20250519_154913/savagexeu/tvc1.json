[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-05-08", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON>", "startDate": "2024-02-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1089864, "name": "Taggstar Experiment Sessions", "total": 2183400, "treatment": 1093536, "type": "long"}, {"adjusted": null, "control": 0.4992, "name": "Experiment split", "total": null, "treatment": 0.5008, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4185720, "name": "Taggstar Experiment Impressions", "total": 8388645, "treatment": 4202925, "type": "long"}, {"adjusted": null, "control": 0.499, "name": "Experiment split", "total": null, "treatment": 0.501, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 3064193, "treatment": 3064193, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.7291, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 30976, "control": 15211, "name": "Number of orders", "total": 30725, "treatment": 15514, "type": "long"}, {"adjusted": 503, "control": null, "name": "Uplift", "total": null, "treatment": 252, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0142, "control": 0.014, "name": "Conversion rate", "total": 0.0141, "treatment": 0.0142, "type": "percentage"}, {"adjusted": 0.0165, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0165, "type": "percentage"}, {"adjusted": null, "control": 0.0137, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0277, "treatment": 0.014, "type": "percentage"}, {"adjusted": null, "control": 0.0142, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0286, "treatment": 0.0144, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0149, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0489, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1071951, "control": 519066, "name": "Revenue", "total": 1055943, "treatment": 536877, "type": "currency"}, {"adjusted": null, "control": 34.12, "name": "AOV", "total": 34.37, "treatment": 34.61, "type": "currency"}, {"adjusted": 32070, "control": null, "name": "Uplift", "total": null, "treatment": 16062, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0165, "actualQuantityUplift": 251.75, "adjustedQuantityUplift": 502.66, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.0743645140639772}}]