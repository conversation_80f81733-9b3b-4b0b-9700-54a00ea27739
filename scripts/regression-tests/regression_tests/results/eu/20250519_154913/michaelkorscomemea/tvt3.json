[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-02-15", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2024-02-02"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1068081, "name": "Taggstar Experiment Sessions", "total": 2134866, "treatment": 1066785, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 7042479, "name": "Taggstar Experiment Impressions", "total": 14113094, "treatment": 7070615, "type": "long"}, {"adjusted": null, "control": 0.499, "name": "Experiment split", "total": null, "treatment": 0.501, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 6374163, "name": "Taggstar Message Impressions", "total": 12774297, "treatment": 6400134, "type": "long"}, {"adjusted": null, "control": 0.9051, "name": "Taggstar coverage", "total": null, "treatment": 0.9052, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 12978, "control": 6450, "name": "Number of orders", "total": 12935, "treatment": 6485, "type": "long"}, {"adjusted": 86, "control": null, "name": "Uplift", "total": null, "treatment": 43, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0061, "control": 0.006, "name": "Conversion rate", "total": 0.0061, "treatment": 0.0061, "type": "percentage"}, {"adjusted": 0.0066, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0066, "type": "percentage"}, {"adjusted": null, "control": 0.0059, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0118, "treatment": 0.0059, "type": "percentage"}, {"adjusted": null, "control": 0.0062, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0124, "treatment": 0.0062, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0411, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0568, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1613420, "control": 808371, "name": "Revenue", "total": 1614591, "treatment": 806220, "type": "currency"}, {"adjusted": null, "control": 125.33, "name": "AOV", "total": 124.82, "treatment": 124.32, "type": "currency"}, {"adjusted": -2340, "control": null, "name": "Uplift", "total": null, "treatment": -1169, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0066, "actualQuantityUplift": 42.83, "adjustedQuantityUplift": 85.7, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.35274156259218914}}]