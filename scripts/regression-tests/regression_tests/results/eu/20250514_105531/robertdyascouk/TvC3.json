[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-08-02", "experimentId": "TvC3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-07-06"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 731329, "name": "Taggstar Experiment Sessions", "total": 1462029, "treatment": 730700, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2338608, "name": "Taggstar Experiment Impressions", "total": 4678441, "treatment": 2339833, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 892908, "treatment": 892908, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.3816, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 42780, "control": 20784, "name": "Number of orders", "total": 42165, "treatment": 21381, "type": "long"}, {"adjusted": 1230, "control": null, "name": "Uplift", "total": null, "treatment": 615, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0293, "control": 0.0284, "name": "Conversion rate", "total": 0.0288, "treatment": 0.0293, "type": "percentage"}, {"adjusted": 0.0296, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0296, "type": "percentage"}, {"adjusted": null, "control": 0.028, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0569, "treatment": 0.0289, "type": "percentage"}, {"adjusted": null, "control": 0.0288, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0584, "treatment": 0.0296, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0026, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0574, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 4956715, "control": 2415494, "name": "Revenue", "total": 4892785, "treatment": 2477291, "type": "currency"}, {"adjusted": null, "control": 116.22, "name": "AOV", "total": 116.04, "treatment": 115.86, "type": "currency"}, {"adjusted": 127803, "control": null, "name": "Uplift", "total": null, "treatment": 63874, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0296, "actualQuantityUplift": 614.88, "adjustedQuantityUplift": 1230.28, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0011834957201742925}}]