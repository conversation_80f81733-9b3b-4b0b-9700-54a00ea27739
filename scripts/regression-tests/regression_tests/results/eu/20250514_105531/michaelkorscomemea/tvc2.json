[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-10-08", "experimentId": "tvc2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2024-08-23"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3530246, "name": "Taggstar Experiment Sessions", "total": 7066683, "treatment": 3536437, "type": "long"}, {"adjusted": null, "control": 0.4996, "name": "Experiment split", "total": null, "treatment": 0.5004, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 21598899, "name": "Taggstar Experiment Impressions", "total": 43107809, "treatment": 21508910, "type": "long"}, {"adjusted": null, "control": 0.501, "name": "Experiment split", "total": null, "treatment": 0.499, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 17815976, "treatment": 17815976, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8283, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 34384, "control": 16613, "name": "Number of orders", "total": 33820, "treatment": 17207, "type": "long"}, {"adjusted": 1129, "control": null, "name": "Uplift", "total": null, "treatment": 565, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0049, "control": 0.0047, "name": "Conversion rate", "total": 0.0048, "treatment": 0.0049, "type": "percentage"}, {"adjusted": 0.0339, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0339, "type": "percentage"}, {"adjusted": null, "control": 0.0046, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0094, "treatment": 0.0048, "type": "percentage"}, {"adjusted": null, "control": 0.0048, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0097, "treatment": 0.0049, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0033, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0655, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 5287540, "control": 2518052, "name": "Revenue", "total": 5164138, "treatment": 2646086, "type": "currency"}, {"adjusted": null, "control": 151.57, "name": "AOV", "total": 152.69, "treatment": 153.78, "type": "currency"}, {"adjusted": 247020, "control": null, "name": "Uplift", "total": null, "treatment": 123618, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0339, "actualQuantityUplift": 564.87, "adjustedQuantityUplift": 1128.74, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.001047993772361949}}]