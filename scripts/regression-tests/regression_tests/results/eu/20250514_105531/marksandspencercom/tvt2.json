[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-05-01", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "marksandspencercom", "startDate": "2024-04-19"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3622289, "name": "Taggstar Experiment Sessions", "total": 7247790, "treatment": 3625501, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 17718879, "name": "Taggstar Experiment Impressions", "total": 35473606, "treatment": 17754727, "type": "long"}, {"adjusted": null, "control": 0.4995, "name": "Experiment split", "total": null, "treatment": 0.5005, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 15313409, "name": "Taggstar Message Impressions", "total": 31983538, "treatment": 16670129, "type": "long"}, {"adjusted": null, "control": 0.8642, "name": "Taggstar coverage", "total": null, "treatment": 0.9389, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 370252, "control": 187999, "name": "Number of orders", "total": 373207, "treatment": 185208, "type": "long"}, {"adjusted": -5913, "control": null, "name": "Uplift", "total": null, "treatment": -2958, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0511, "control": 0.0519, "name": "Conversion rate", "total": 0.0515, "treatment": 0.0511, "type": "percentage"}, {"adjusted": -0.0157, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0157, "type": "percentage"}, {"adjusted": null, "control": 0.0517, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1025, "treatment": 0.0509, "type": "percentage"}, {"adjusted": null, "control": 0.0521, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1034, "treatment": 0.0513, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0244, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": -0.007, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 23197976, "control": 11776397, "name": "Revenue", "total": 23380525, "treatment": 11604128, "type": "currency"}, {"adjusted": null, "control": 62.64, "name": "AOV", "total": 62.65, "treatment": 62.65, "type": "currency"}, {"adjusted": -365260, "control": null, "name": "Uplift", "total": null, "treatment": -182711, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0157, "actualQuantityUplift": -2957.7, "adjustedQuantityUplift": -5912.79, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9999996634527201}}]