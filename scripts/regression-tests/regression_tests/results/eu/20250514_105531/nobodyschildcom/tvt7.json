[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-07-22", "experimentId": "tvt7", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "nobodyschildcom", "startDate": "2024-07-18"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 155777, "name": "Taggstar Experiment Sessions", "total": 310880, "treatment": 155103, "type": "long"}, {"adjusted": null, "control": 0.5011, "name": "Experiment split", "total": null, "treatment": 0.4989, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1003522, "name": "Taggstar Experiment Impressions", "total": 2010814, "treatment": 1007292, "type": "long"}, {"adjusted": null, "control": 0.4991, "name": "Experiment split", "total": null, "treatment": 0.5009, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 793059, "name": "Taggstar Message Impressions", "total": 1592034, "treatment": 798975, "type": "long"}, {"adjusted": null, "control": 0.7903, "name": "Taggstar coverage", "total": null, "treatment": 0.7932, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 5805, "control": 2791, "name": "Number of orders", "total": 5687, "treatment": 2896, "type": "long"}, {"adjusted": 235, "control": null, "name": "Uplift", "total": null, "treatment": 117, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0187, "control": 0.0179, "name": "Conversion rate", "total": 0.0183, "treatment": 0.0187, "type": "percentage"}, {"adjusted": 0.0421, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0421, "type": "percentage"}, {"adjusted": null, "control": 0.0173, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0353, "treatment": 0.018, "type": "percentage"}, {"adjusted": null, "control": 0.0186, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0379, "treatment": 0.0193, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0311, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.1209, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 700781, "control": 327968, "name": "Revenue", "total": 677598, "treatment": 349631, "type": "currency"}, {"adjusted": null, "control": 117.51, "name": "AOV", "total": 119.15, "treatment": 120.73, "type": "currency"}, {"adjusted": 46265, "control": null, "name": "Uplift", "total": null, "treatment": 23082, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0421, "actualQuantityUplift": 117.08, "adjustedQuantityUplift": 234.66, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.058182641367484744}}]