[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-09-18", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "ninja<PERSON>tchen<PERSON>uk", "startDate": "2024-08-14"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1189898, "name": "Taggstar Experiment Sessions", "total": 2385289, "treatment": 1195391, "type": "long"}, {"adjusted": null, "control": 0.4988, "name": "Experiment split", "total": null, "treatment": 0.5012, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2780210, "name": "Taggstar Experiment Impressions", "total": 5573257, "treatment": 2793047, "type": "long"}, {"adjusted": null, "control": 0.4988, "name": "Experiment split", "total": null, "treatment": 0.5012, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 2655145, "name": "Taggstar Message Impressions", "total": 5345184, "treatment": 2690039, "type": "long"}, {"adjusted": null, "control": 0.955, "name": "Taggstar coverage", "total": null, "treatment": 0.9631, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 60567, "control": 30410, "name": "Number of orders", "total": 60763, "treatment": 30353, "type": "long"}, {"adjusted": -394, "control": null, "name": "Uplift", "total": null, "treatment": -197, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0254, "control": 0.0256, "name": "Conversion rate", "total": 0.0255, "treatment": 0.0254, "type": "percentage"}, {"adjusted": -0.0065, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0065, "type": "percentage"}, {"adjusted": null, "control": 0.0253, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0504, "treatment": 0.0251, "type": "percentage"}, {"adjusted": null, "control": 0.0258, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0515, "treatment": 0.0257, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0283, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0158, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 8976388, "control": 4619011, "name": "Revenue", "total": 9117540, "treatment": 4498530, "type": "currency"}, {"adjusted": null, "control": 151.89, "name": "AOV", "total": 150.05, "treatment": 148.21, "type": "currency"}, {"adjusted": -282956, "control": null, "name": "Uplift", "total": null, "treatment": -141804, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0065, "actualQuantityUplift": -197.38, "adjustedQuantityUplift": -393.86, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7908190174862895}}]