[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2020-12-03", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "sportsshoescom", "startDate": "2020-11-17"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1928876, "name": "Taggstar Experiment Sessions", "total": 3863894, "treatment": 1935018, "type": "long"}, {"adjusted": null, "control": 0.4992, "name": "Experiment split", "total": null, "treatment": 0.5008, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 7435368, "name": "Taggstar Experiment Impressions", "total": 14961181, "treatment": 7525813, "type": "long"}, {"adjusted": null, "control": 0.497, "name": "Experiment split", "total": null, "treatment": 0.503, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 6212574, "treatment": 6212574, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8255, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 85790, "control": 40544, "name": "Number of orders", "total": 83507, "treatment": 42963, "type": "long"}, {"adjusted": 4573, "control": null, "name": "Uplift", "total": null, "treatment": 2290, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0222, "control": 0.021, "name": "Conversion rate", "total": 0.0216, "treatment": 0.0222, "type": "percentage"}, {"adjusted": 0.0563, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0563, "type": "percentage"}, {"adjusted": null, "control": 0.0208, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0428, "treatment": 0.022, "type": "percentage"}, {"adjusted": null, "control": 0.0212, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0436, "treatment": 0.0224, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0364, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0765, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 0, "control": 0, "name": "Revenue", "total": 0, "treatment": 0, "type": "currency"}, {"adjusted": null, "control": 0.0, "name": "AOV", "total": 0.0, "treatment": 0.0, "type": "currency"}, {"adjusted": 0, "control": null, "name": "Uplift", "total": null, "treatment": 0, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0563, "actualQuantityUplift": 2289.9, "adjustedQuantityUplift": 4572.53, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 6.276142783785789e-16}}]