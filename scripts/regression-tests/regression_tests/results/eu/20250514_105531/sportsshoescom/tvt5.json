[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-11-29", "experimentId": "tvt5", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "sportsshoescom", "startDate": "2022-11-12"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1054491, "name": "Taggstar Experiment Sessions", "total": 2106310, "treatment": 1051819, "type": "long"}, {"adjusted": null, "control": 0.5006, "name": "Experiment split", "total": null, "treatment": 0.4994, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4407331, "name": "Taggstar Experiment Impressions", "total": 8802023, "treatment": 4394692, "type": "long"}, {"adjusted": null, "control": 0.5007, "name": "Experiment split", "total": null, "treatment": 0.4993, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 2681919, "name": "Taggstar Message Impressions", "total": 5366967, "treatment": 2685048, "type": "long"}, {"adjusted": null, "control": 0.6085, "name": "Taggstar coverage", "total": null, "treatment": 0.611, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 32165, "control": 16142, "name": "Number of orders", "total": 32204, "treatment": 16062, "type": "long"}, {"adjusted": -78, "control": null, "name": "Uplift", "total": null, "treatment": -39, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0153, "control": 0.0153, "name": "Conversion rate", "total": 0.0153, "treatment": 0.0153, "type": "percentage"}, {"adjusted": -0.0024, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0024, "type": "percentage"}, {"adjusted": null, "control": 0.0151, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0301, "treatment": 0.015, "type": "percentage"}, {"adjusted": null, "control": 0.0155, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.031, "treatment": 0.0155, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0325, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0286, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3106895, "control": 1584349, "name": "Revenue", "total": 3135826, "treatment": 1551477, "type": "currency"}, {"adjusted": null, "control": 98.15, "name": "AOV", "total": 97.37, "treatment": 96.59, "type": "currency"}, {"adjusted": -57788, "control": null, "name": "Uplift", "total": null, "treatment": -28857, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0024, "actualQuantityUplift": -39.1, "adjustedQuantityUplift": -78.29, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.5869988677860473}}]