[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-06-05", "experimentId": "tvt5", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2024-05-15"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 841018, "name": "Taggstar Experiment Sessions", "total": 1677431, "treatment": 836413, "type": "long"}, {"adjusted": null, "control": 0.5014, "name": "Experiment split", "total": null, "treatment": 0.4986, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6050772, "name": "Taggstar Experiment Impressions", "total": 12041982, "treatment": 5991210, "type": "long"}, {"adjusted": null, "control": 0.5025, "name": "Experiment split", "total": null, "treatment": 0.4975, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 5230985, "name": "Taggstar Message Impressions", "total": 10411813, "treatment": 5180828, "type": "long"}, {"adjusted": null, "control": 0.8645, "name": "Taggstar coverage", "total": null, "treatment": 0.8647, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 88258, "control": 44655, "name": "Number of orders", "total": 88663, "treatment": 44008, "type": "long"}, {"adjusted": -807, "control": null, "name": "Uplift", "total": null, "treatment": -402, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0526, "control": 0.0531, "name": "Conversion rate", "total": 0.0529, "treatment": 0.0526, "type": "percentage"}, {"adjusted": -0.0091, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0091, "type": "percentage"}, {"adjusted": null, "control": 0.0526, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1048, "treatment": 0.0521, "type": "percentage"}, {"adjusted": null, "control": 0.0536, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1067, "treatment": 0.0531, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0269, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0091, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 7842975, "control": 4005883, "name": "Revenue", "total": 7916605, "treatment": 3910722, "type": "currency"}, {"adjusted": null, "control": 89.71, "name": "AOV", "total": 89.29, "treatment": 88.86, "type": "currency"}, {"adjusted": -146856, "control": null, "name": "Uplift", "total": null, "treatment": -73226, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0091, "actualQuantityUplift": -402.49, "adjustedQuantityUplift": -807.2, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.9181531571457198}}]