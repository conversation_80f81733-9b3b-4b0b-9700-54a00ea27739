[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2020-09-21", "experimentId": "experiment3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "simplybecouk", "startDate": "2020-01-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1020325, "name": "Taggstar Experiment Sessions", "total": 20256082, "treatment": 19235757, "type": "long"}, {"adjusted": null, "control": 0.0504, "name": "Experiment split", "total": null, "treatment": 0.9496, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3496508, "name": "Taggstar Experiment Impressions", "total": 70163446, "treatment": 66666938, "type": "long"}, {"adjusted": null, "control": 0.0498, "name": "Experiment split", "total": null, "treatment": 0.9502, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 38865948, "treatment": 38865948, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.583, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 1414197, "control": 69949, "name": "Number of orders", "total": 1412911, "treatment": 1342962, "type": "long"}, {"adjusted": 25529, "control": null, "name": "Uplift", "total": null, "treatment": 24243, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0698, "control": 0.0686, "name": "Conversion rate", "total": 0.0692, "treatment": 0.0698, "type": "percentage"}, {"adjusted": 0.0184, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0184, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 93081369, "control": 4605588, "name": "Revenue", "total": 92998328, "treatment": 88392740, "type": "currency"}, {"adjusted": null, "control": 65.84, "name": "AOV", "total": 65.82, "treatment": 65.82, "type": "currency"}, {"adjusted": 1648575, "control": null, "name": "Uplift", "total": null, "treatment": 1565534, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0184, "actualQuantityUplift": 24243.0, "adjustedQuantityUplift": 25528.92, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 4.618341628570555e-07}}]