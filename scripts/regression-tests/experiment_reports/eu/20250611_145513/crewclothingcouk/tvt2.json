[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-11-11", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "crewclothing<PERSON><PERSON>", "startDate": "2022-08-22"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2119123, "name": "Taggstar Experiment Sessions", "total": 4237214, "treatment": 2118091, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 11781434, "name": "Taggstar Experiment Impressions", "total": 23578153, "treatment": 11796719, "type": "long"}, {"adjusted": null, "control": 0.4997, "name": "Experiment split", "total": null, "treatment": 0.5003, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 9992150, "name": "Taggstar Message Impressions", "total": 20002439, "treatment": 10010289, "type": "long"}, {"adjusted": null, "control": 0.8481, "name": "Taggstar coverage", "total": null, "treatment": 0.8486, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 119551, "control": 59817, "name": "Number of orders", "total": 119578, "treatment": 59761, "type": "long"}, {"adjusted": -54, "control": null, "name": "Uplift", "total": null, "treatment": -27, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0282, "control": 0.0282, "name": "Conversion rate", "total": 0.0282, "treatment": 0.0282, "type": "percentage"}, {"adjusted": -0.0004, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0004, "type": "percentage"}, {"adjusted": null, "control": 0.028, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.056, "treatment": 0.028, "type": "percentage"}, {"adjusted": null, "control": 0.0285, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0569, "treatment": 0.0284, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0161, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0155, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 8941936, "control": 4487173, "name": "Revenue", "total": 8957053, "treatment": 4469879, "type": "currency"}, {"adjusted": null, "control": 75.02, "name": "AOV", "total": 74.91, "treatment": 74.8, "type": "currency"}, {"adjusted": -30225, "control": null, "name": "Uplift", "total": null, "treatment": -15109, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0004, "actualQuantityUplift": -26.87, "adjustedQuantityUplift": -53.75, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.5314207137928574}}]