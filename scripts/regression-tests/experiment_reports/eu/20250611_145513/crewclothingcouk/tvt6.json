[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-06-03", "experimentId": "tvt6", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "crewclothing<PERSON><PERSON>", "startDate": "2024-04-11"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1296622, "name": "Taggstar Experiment Sessions", "total": 2595585, "treatment": 1298963, "type": "long"}, {"adjusted": null, "control": 0.4995, "name": "Experiment split", "total": null, "treatment": 0.5005, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 8092136, "name": "Taggstar Experiment Impressions", "total": 16199208, "treatment": 8107072, "type": "long"}, {"adjusted": null, "control": 0.4995, "name": "Experiment split", "total": null, "treatment": 0.5005, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 6817431, "name": "Taggstar Message Impressions", "total": 13641548, "treatment": 6824117, "type": "long"}, {"adjusted": null, "control": 0.8425, "name": "Taggstar coverage", "total": null, "treatment": 0.8417, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 103635, "control": 51427, "name": "Number of orders", "total": 103291, "treatment": 51864, "type": "long"}, {"adjusted": 688, "control": null, "name": "Uplift", "total": null, "treatment": 344, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0399, "control": 0.0397, "name": "Conversion rate", "total": 0.0398, "treatment": 0.0399, "type": "percentage"}, {"adjusted": 0.0067, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0067, "type": "percentage"}, {"adjusted": null, "control": 0.0393, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0789, "treatment": 0.0396, "type": "percentage"}, {"adjusted": null, "control": 0.04, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0803, "treatment": 0.0403, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0102, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0238, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 8048482, "control": 3977727, "name": "Revenue", "total": 8005597, "treatment": 4027870, "type": "currency"}, {"adjusted": null, "control": 77.35, "name": "AOV", "total": 77.51, "treatment": 77.66, "type": "currency"}, {"adjusted": 85846, "control": null, "name": "Uplift", "total": null, "treatment": 42962, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0067, "actualQuantityUplift": 344.15, "adjustedQuantityUplift": 687.68, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.13745950774185245}}]