[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-12-02", "experimentId": "tvt7", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "crewclothing<PERSON><PERSON>", "startDate": "2024-10-30"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1119088, "name": "Taggstar Experiment Sessions", "total": 2236188, "treatment": 1117100, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 8260147, "name": "Taggstar Experiment Impressions", "total": 16546010, "treatment": 8285863, "type": "long"}, {"adjusted": null, "control": 0.4992, "name": "Experiment split", "total": null, "treatment": 0.5008, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 7166776, "name": "Taggstar Message Impressions", "total": 13992602, "treatment": 6825826, "type": "long"}, {"adjusted": null, "control": 0.8676, "name": "Taggstar coverage", "total": null, "treatment": 0.8238, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 129211, "control": 63849, "name": "Number of orders", "total": 128397, "treatment": 64548, "type": "long"}, {"adjusted": 1626, "control": null, "name": "Uplift", "total": null, "treatment": 812, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0578, "control": 0.0571, "name": "Conversion rate", "total": 0.0574, "treatment": 0.0578, "type": "percentage"}, {"adjusted": 0.0127, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0127, "type": "percentage"}, {"adjusted": null, "control": 0.0566, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.114, "treatment": 0.0573, "type": "percentage"}, {"adjusted": null, "control": 0.0575, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1157, "treatment": 0.0582, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0024, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0281, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 9729700, "control": 4787811, "name": "Revenue", "total": 9648336, "treatment": 4860525, "type": "currency"}, {"adjusted": null, "control": 74.99, "name": "AOV", "total": 75.14, "treatment": 75.3, "type": "currency"}, {"adjusted": 162584, "control": null, "name": "Uplift", "total": null, "treatment": 81220, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0127, "actualQuantityUplift": 812.42, "adjustedQuantityUplift": 1626.29, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.009709595043446293}}]