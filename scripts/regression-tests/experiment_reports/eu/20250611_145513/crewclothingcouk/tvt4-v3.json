[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-09-11", "experimentId": "tvt4-v3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "crewclothing<PERSON><PERSON>", "startDate": "2023-08-18"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 636893, "name": "Taggstar Experiment Sessions", "total": 1273405, "treatment": 636512, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4088796, "name": "Taggstar Experiment Impressions", "total": 8206193, "treatment": 4117397, "type": "long"}, {"adjusted": null, "control": 0.4983, "name": "Experiment split", "total": null, "treatment": 0.5017, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 3477546, "name": "Taggstar Message Impressions", "total": 6977433, "treatment": 3499887, "type": "long"}, {"adjusted": null, "control": 0.8505, "name": "Taggstar coverage", "total": null, "treatment": 0.85, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 48691, "control": 24740, "name": "Number of orders", "total": 49078, "treatment": 24338, "type": "long"}, {"adjusted": -775, "control": null, "name": "Uplift", "total": null, "treatment": -387, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0382, "control": 0.0388, "name": "Conversion rate", "total": 0.0385, "treatment": 0.0382, "type": "percentage"}, {"adjusted": -0.0157, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0157, "type": "percentage"}, {"adjusted": null, "control": 0.0384, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0761, "treatment": 0.0378, "type": "percentage"}, {"adjusted": null, "control": 0.0393, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.078, "treatment": 0.0387, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0395, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0088, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3504638, "control": 1757422, "name": "Revenue", "total": 3509217, "treatment": 1751795, "type": "currency"}, {"adjusted": null, "control": 71.04, "name": "AOV", "total": 71.5, "treatment": 71.98, "type": "currency"}, {"adjusted": -9156, "control": null, "name": "Uplift", "total": null, "treatment": -4577, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0157, "actualQuantityUplift": -387.2, "adjustedQuantityUplift": -774.63, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9627091005584134}}]