[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-12-29", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "crewclothing<PERSON><PERSON>", "startDate": "2022-12-06"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 790391, "name": "Taggstar Experiment Sessions", "total": 1578685, "treatment": 788294, "type": "long"}, {"adjusted": null, "control": 0.5007, "name": "Experiment split", "total": null, "treatment": 0.4993, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5336765, "name": "Taggstar Experiment Impressions", "total": 10658239, "treatment": 5321474, "type": "long"}, {"adjusted": null, "control": 0.5007, "name": "Experiment split", "total": null, "treatment": 0.4993, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 4686244, "name": "Taggstar Message Impressions", "total": 9362903, "treatment": 4676659, "type": "long"}, {"adjusted": null, "control": 0.8781, "name": "Taggstar coverage", "total": null, "treatment": 0.8788, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 60783, "control": 30460, "name": "Number of orders", "total": 60811, "treatment": 30351, "type": "long"}, {"adjusted": -56, "control": null, "name": "Uplift", "total": null, "treatment": -28, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0385, "control": 0.0385, "name": "Conversion rate", "total": 0.0385, "treatment": 0.0385, "type": "percentage"}, {"adjusted": -0.0009, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0009, "type": "percentage"}, {"adjusted": null, "control": 0.0381, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0762, "treatment": 0.0381, "type": "percentage"}, {"adjusted": null, "control": 0.039, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0779, "treatment": 0.0389, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0227, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0213, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 4110242, "control": 2072617, "name": "Revenue", "total": 4125008, "treatment": 2052391, "type": "currency"}, {"adjusted": null, "control": 68.04, "name": "AOV", "total": 67.83, "treatment": 67.62, "type": "currency"}, {"adjusted": -29494, "control": null, "name": "Uplift", "total": null, "treatment": -14727, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0009, "actualQuantityUplift": -28.19, "adjustedQuantityUplift": -56.45, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.5464595475399983}}]