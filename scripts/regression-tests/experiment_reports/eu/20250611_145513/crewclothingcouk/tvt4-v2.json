[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-08-17", "experimentId": "tvt4-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "crewclothing<PERSON><PERSON>", "startDate": "2023-07-13"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 978614, "name": "Taggstar Experiment Sessions", "total": 1957279, "treatment": 978665, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5842454, "name": "Taggstar Experiment Impressions", "total": 11655284, "treatment": 5812830, "type": "long"}, {"adjusted": null, "control": 0.5013, "name": "Experiment split", "total": null, "treatment": 0.4987, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 4934229, "name": "Taggstar Message Impressions", "total": 9841326, "treatment": 4907097, "type": "long"}, {"adjusted": null, "control": 0.8445, "name": "Taggstar coverage", "total": null, "treatment": 0.8442, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 66502, "control": 33307, "name": "Number of orders", "total": 66559, "treatment": 33252, "type": "long"}, {"adjusted": -113, "control": null, "name": "Uplift", "total": null, "treatment": -57, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.034, "control": 0.034, "name": "Conversion rate", "total": 0.034, "treatment": 0.034, "type": "percentage"}, {"adjusted": -0.0017, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0017, "type": "percentage"}, {"adjusted": null, "control": 0.0337, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0673, "treatment": 0.0336, "type": "percentage"}, {"adjusted": null, "control": 0.0344, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0687, "treatment": 0.0343, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0226, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0196, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 4897992, "control": 2459522, "name": "Revenue", "total": 4908581, "treatment": 2449060, "type": "currency"}, {"adjusted": null, "control": 73.84, "name": "AOV", "total": 73.75, "treatment": 73.65, "type": "currency"}, {"adjusted": -21180, "control": null, "name": "Uplift", "total": null, "treatment": -10590, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0017, "actualQuantityUplift": -56.74, "adjustedQuantityUplift": -113.47, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.588522481172069}}]