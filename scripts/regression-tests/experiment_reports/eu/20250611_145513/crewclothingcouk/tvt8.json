[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-12-30", "experimentId": "tvt8", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "crewclothing<PERSON><PERSON>", "startDate": "2024-12-04"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 906729, "name": "Taggstar Experiment Sessions", "total": 1811238, "treatment": 904509, "type": "long"}, {"adjusted": null, "control": 0.5006, "name": "Experiment split", "total": null, "treatment": 0.4994, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 7909511, "name": "Taggstar Experiment Impressions", "total": 15762732, "treatment": 7853221, "type": "long"}, {"adjusted": null, "control": 0.5018, "name": "Experiment split", "total": null, "treatment": 0.4982, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 6874606, "name": "Taggstar Message Impressions", "total": 13713497, "treatment": 6838891, "type": "long"}, {"adjusted": null, "control": 0.8692, "name": "Taggstar coverage", "total": null, "treatment": 0.8708, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 121535, "control": 60412, "name": "Number of orders", "total": 121105, "treatment": 60693, "type": "long"}, {"adjusted": 859, "control": null, "name": "Uplift", "total": null, "treatment": 429, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0671, "control": 0.0666, "name": "Conversion rate", "total": 0.0669, "treatment": 0.0671, "type": "percentage"}, {"adjusted": 0.0071, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0071, "type": "percentage"}, {"adjusted": null, "control": 0.0661, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1327, "treatment": 0.0666, "type": "percentage"}, {"adjusted": null, "control": 0.0671, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1348, "treatment": 0.0676, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0083, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0227, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 7964397, "control": 3968003, "name": "Revenue", "total": 7945321, "treatment": 3977317, "type": "currency"}, {"adjusted": null, "control": 65.68, "name": "AOV", "total": 65.61, "treatment": 65.53, "type": "currency"}, {"adjusted": 38105, "control": null, "name": "Uplift", "total": null, "treatment": 19029, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0071, "actualQuantityUplift": 428.91, "adjustedQuantityUplift": 858.87, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.10072187236579094}}]