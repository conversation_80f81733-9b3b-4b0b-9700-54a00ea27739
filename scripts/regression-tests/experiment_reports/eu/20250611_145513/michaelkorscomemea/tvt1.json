[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-08-16", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-07-19"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3408531, "name": "Taggstar Experiment Sessions", "total": 6828044, "treatment": 3419513, "type": "long"}, {"adjusted": null, "control": 0.4992, "name": "Experiment split", "total": null, "treatment": 0.5008, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 25571315, "name": "Taggstar Experiment Impressions", "total": 51206123, "treatment": 25634808, "type": "long"}, {"adjusted": null, "control": 0.4994, "name": "Experiment split", "total": null, "treatment": 0.5006, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 24775686, "name": "Taggstar Message Impressions", "total": 49951089, "treatment": 25175403, "type": "long"}, {"adjusted": null, "control": 0.9689, "name": "Taggstar coverage", "total": null, "treatment": 0.9821, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 61461, "control": 31428, "name": "Number of orders", "total": 62208, "treatment": 30780, "type": "long"}, {"adjusted": -1496, "control": null, "name": "Uplift", "total": null, "treatment": -749, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.009, "control": 0.0092, "name": "Conversion rate", "total": 0.0091, "treatment": 0.009, "type": "percentage"}, {"adjusted": -0.0238, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0238, "type": "percentage"}, {"adjusted": null, "control": 0.0091, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.018, "treatment": 0.0089, "type": "percentage"}, {"adjusted": null, "control": 0.0093, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0184, "treatment": 0.0091, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0451, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": -0.0019, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 8019906, "control": 4091870, "name": "Revenue", "total": 8108272, "treatment": 4016403, "type": "currency"}, {"adjusted": null, "control": 130.2, "name": "AOV", "total": 130.34, "treatment": 130.49, "type": "currency"}, {"adjusted": -177017, "control": null, "name": "Uplift", "total": null, "treatment": -88651, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0238, "actualQuantityUplift": -749.26, "adjustedQuantityUplift": -1496.11, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9987063166763791}}]