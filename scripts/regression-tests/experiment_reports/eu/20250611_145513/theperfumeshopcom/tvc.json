[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2021-09-02", "experimentId": "tvc", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "theperfumeshopcom", "startDate": "2021-08-03"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 658363, "name": "Taggstar Experiment Sessions", "total": 1317682, "treatment": 659319, "type": "long"}, {"adjusted": null, "control": 0.4996, "name": "Experiment split", "total": null, "treatment": 0.5004, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2049274, "name": "Taggstar Experiment Impressions", "total": 4098226, "treatment": 2048952, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 1646465, "treatment": 1646465, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8036, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 65329, "control": 32459, "name": "Number of orders", "total": 65147, "treatment": 32688, "type": "long"}, {"adjusted": 363, "control": null, "name": "Uplift", "total": null, "treatment": 182, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0496, "control": 0.0493, "name": "Conversion rate", "total": 0.0494, "treatment": 0.0496, "type": "percentage"}, {"adjusted": 0.0056, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0056, "type": "percentage"}, {"adjusted": null, "control": 0.0488, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0978, "treatment": 0.0491, "type": "percentage"}, {"adjusted": null, "control": 0.0498, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0999, "treatment": 0.0501, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0155, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0271, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3665256, "control": 1839950, "name": "Revenue", "total": 3673908, "treatment": 1833958, "type": "currency"}, {"adjusted": null, "control": 56.69, "name": "AOV", "total": 56.39, "treatment": 56.1, "type": "currency"}, {"adjusted": -17316, "control": null, "name": "Uplift", "total": null, "treatment": -8664, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0056, "actualQuantityUplift": 181.87, "adjustedQuantityUplift": 363.47, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.2326029306654972}}]