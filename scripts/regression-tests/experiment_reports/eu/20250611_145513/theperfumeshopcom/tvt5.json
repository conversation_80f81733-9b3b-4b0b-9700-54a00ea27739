[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-05-19", "experimentId": "tvt5", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "theperfumeshopcom", "startDate": "2025-04-01"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 769422, "name": "Taggstar Experiment Sessions", "total": 1538424, "treatment": 769002, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2793293, "name": "Taggstar Experiment Impressions", "total": 5586834, "treatment": 2793541, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 2587411, "name": "Taggstar Message Impressions", "total": 5172274, "treatment": 2584863, "type": "long"}, {"adjusted": null, "control": 0.9263, "name": "Taggstar coverage", "total": null, "treatment": 0.9253, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 60757, "control": 30204, "name": "Number of orders", "total": 60574, "treatment": 30370, "type": "long"}, {"adjusted": 365, "control": null, "name": "Uplift", "total": null, "treatment": 182, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0395, "control": 0.0393, "name": "Conversion rate", "total": 0.0394, "treatment": 0.0395, "type": "percentage"}, {"adjusted": 0.006, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.006, "type": "percentage"}, {"adjusted": null, "control": 0.0388, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0779, "treatment": 0.0391, "type": "percentage"}, {"adjusted": null, "control": 0.0397, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0796, "treatment": 0.0399, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0159, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0285, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 4732401, "control": 2308719, "name": "Revenue", "total": 4674274, "treatment": 2365554, "type": "currency"}, {"adjusted": null, "control": 76.44, "name": "AOV", "total": 77.17, "treatment": 77.89, "type": "currency"}, {"adjusted": 116222, "control": null, "name": "Uplift", "total": null, "treatment": 58095, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.006, "actualQuantityUplift": 182.49, "adjustedQuantityUplift": 365.07, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.22461120594583844}}]