[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-07-12", "experimentId": "tvc2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "theperfumeshopcom", "startDate": "2021-09-14"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2645274, "name": "Taggstar Experiment Sessions", "total": 21436229, "treatment": 18790955, "type": "long"}, {"adjusted": null, "control": 0.1234, "name": "Experiment split", "total": null, "treatment": 0.8766, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 9392712, "name": "Taggstar Experiment Impressions", "total": 72720826, "treatment": 63328114, "type": "long"}, {"adjusted": null, "control": 0.1292, "name": "Experiment split", "total": null, "treatment": 0.8708, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 58120951, "treatment": 58120951, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9178, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 1029535, "control": 119668, "name": "Number of orders", "total": 1022156, "treatment": 902488, "type": "long"}, {"adjusted": 59794, "control": null, "name": "Uplift", "total": null, "treatment": 52415, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.048, "control": 0.0452, "name": "Conversion rate", "total": 0.0466, "treatment": 0.048, "type": "percentage"}, {"adjusted": 0.0617, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0617, "type": "percentage"}, {"adjusted": null, "control": 0.045, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0929, "treatment": 0.0479, "type": "percentage"}, {"adjusted": null, "control": 0.0455, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0936, "treatment": 0.0481, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0537, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0697, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 64373901, "control": 7579410, "name": "Revenue", "total": 64009442, "treatment": 56430032, "type": "currency"}, {"adjusted": null, "control": 63.34, "name": "AOV", "total": 62.62, "treatment": 62.53, "type": "currency"}, {"adjusted": 2953429, "control": null, "name": "Uplift", "total": null, "treatment": 2588970, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0617, "actualQuantityUplift": 52415.0, "adjustedQuantityUplift": 59793.66, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.720351255397176e-92}}]