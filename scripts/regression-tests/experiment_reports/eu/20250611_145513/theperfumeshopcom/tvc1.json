[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2021-09-13", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "theperfumeshopcom", "startDate": "2021-09-03"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 234323, "name": "Taggstar Experiment Sessions", "total": 467991, "treatment": 233668, "type": "long"}, {"adjusted": null, "control": 0.5007, "name": "Experiment split", "total": null, "treatment": 0.4993, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 739153, "name": "Taggstar Experiment Impressions", "total": 1477536, "treatment": 738383, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 592221, "treatment": 592221, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8021, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 19099, "control": 9554, "name": "Number of orders", "total": 19090, "treatment": 9536, "type": "long"}, {"adjusted": 17, "control": null, "name": "Uplift", "total": null, "treatment": 9, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0408, "control": 0.0408, "name": "Conversion rate", "total": 0.0408, "treatment": 0.0408, "type": "percentage"}, {"adjusted": 0.0009, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0009, "type": "percentage"}, {"adjusted": null, "control": 0.04, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.08, "treatment": 0.04, "type": "percentage"}, {"adjusted": null, "control": 0.0416, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0832, "treatment": 0.0416, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0377, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.041, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1206621, "control": 606175, "name": "Revenue", "total": 1208641, "treatment": 602466, "type": "currency"}, {"adjusted": null, "control": 63.45, "name": "AOV", "total": 63.31, "treatment": 63.18, "type": "currency"}, {"adjusted": -4034, "control": null, "name": "Uplift", "total": null, "treatment": -2014, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0009, "actualQuantityUplift": 8.71, "adjustedQuantityUplift": 17.44, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.47431468263876}}]