[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-03-28", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "whitestorescouk", "startDate": "2024-03-27"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4159, "name": "Taggstar Experiment Sessions", "total": 8387, "treatment": 4228, "type": "long"}, {"adjusted": null, "control": 0.4959, "name": "Experiment split", "total": null, "treatment": 0.5041, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 12897, "name": "Taggstar Experiment Impressions", "total": 26262, "treatment": 13365, "type": "long"}, {"adjusted": null, "control": 0.4911, "name": "Experiment split", "total": null, "treatment": 0.5089, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 11255, "treatment": 11255, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8421, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 87, "control": 35, "name": "Number of orders", "total": 79, "treatment": 44, "type": "long"}, {"adjusted": 17, "control": null, "name": "Uplift", "total": null, "treatment": 8, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0104, "control": 0.0084, "name": "Conversion rate", "total": 0.0094, "treatment": 0.0104, "type": "percentage"}, {"adjusted": 0.2366, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.2366, "type": "percentage"}, {"adjusted": null, "control": 0.0056, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.013, "treatment": 0.0073, "type": "percentage"}, {"adjusted": null, "control": 0.0112, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0247, "treatment": 0.0135, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.3435, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 1.3879, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 57866, "control": 26371, "name": "Revenue", "total": 55542, "treatment": 29171, "type": "currency"}, {"adjusted": null, "control": 753.45, "name": "AOV", "total": 703.06, "treatment": 662.97, "type": "currency"}, {"adjusted": 4686, "control": null, "name": "Uplift", "total": null, "treatment": 2362, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.2366, "actualQuantityUplift": 8.42, "adjustedQuantityUplift": 16.7, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.17237841015887034}}]