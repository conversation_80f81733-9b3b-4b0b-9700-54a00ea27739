[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-08-08", "experimentId": "tvt4", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "marksandspencercom", "startDate": "2024-08-01"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2555713, "name": "Taggstar Experiment Sessions", "total": 5106843, "treatment": 2551130, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 16671026, "name": "Taggstar Experiment Impressions", "total": 33307952, "treatment": 16636926, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 15193096, "name": "Taggstar Message Impressions", "total": 30349664, "treatment": 15156568, "type": "long"}, {"adjusted": null, "control": 0.9113, "name": "Taggstar coverage", "total": null, "treatment": 0.911, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 311093, "control": 155668, "name": "Number of orders", "total": 311075, "treatment": 155407, "type": "long"}, {"adjusted": 36, "control": null, "name": "Uplift", "total": null, "treatment": 18, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0609, "control": 0.0609, "name": "Conversion rate", "total": 0.0609, "treatment": 0.0609, "type": "percentage"}, {"adjusted": 0.0001, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0001, "type": "percentage"}, {"adjusted": null, "control": 0.0606, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1212, "treatment": 0.0606, "type": "percentage"}, {"adjusted": null, "control": 0.0612, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1224, "treatment": 0.0612, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0095, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0098, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 17634857, "control": 8853682, "name": "Revenue", "total": 17663197, "treatment": 8809515, "type": "currency"}, {"adjusted": null, "control": 56.88, "name": "AOV", "total": 56.78, "treatment": 56.69, "type": "currency"}, {"adjusted": -56630, "control": null, "name": "Uplift", "total": null, "treatment": -28289, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0001, "actualQuantityUplift": 18.15, "adjustedQuantityUplift": 36.33, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.4865939322323408}}]