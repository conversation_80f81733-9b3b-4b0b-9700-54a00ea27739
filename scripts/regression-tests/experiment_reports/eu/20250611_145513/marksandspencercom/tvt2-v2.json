[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-06-11", "experimentId": "tvt2-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "marksandspencercom", "startDate": "2024-05-15"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 8812076, "name": "Taggstar Experiment Sessions", "total": 17626502, "treatment": 8814426, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 45402546, "name": "Taggstar Experiment Impressions", "total": 90816945, "treatment": 45414399, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 39638630, "name": "Taggstar Message Impressions", "total": 81710659, "treatment": 42072029, "type": "long"}, {"adjusted": null, "control": 0.873, "name": "Taggstar coverage", "total": null, "treatment": 0.9264, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 972588, "control": 490436, "name": "Number of orders", "total": 976795, "treatment": 486359, "type": "long"}, {"adjusted": -8414, "control": null, "name": "Uplift", "total": null, "treatment": -4208, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0552, "control": 0.0557, "name": "Conversion rate", "total": 0.0554, "treatment": 0.0552, "type": "percentage"}, {"adjusted": -0.0086, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0086, "type": "percentage"}, {"adjusted": null, "control": 0.0555, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1105, "treatment": 0.055, "type": "percentage"}, {"adjusted": null, "control": 0.0558, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1111, "treatment": 0.0553, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.014, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": -0.0032, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 64330122, "control": 32286027, "name": "Revenue", "total": 64455376, "treatment": 32169349, "type": "currency"}, {"adjusted": null, "control": 65.83, "name": "AOV", "total": 65.99, "treatment": 66.14, "type": "currency"}, {"adjusted": -250542, "control": null, "name": "Uplift", "total": null, "treatment": -125288, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0086, "actualQuantityUplift": -4207.79, "adjustedQuantityUplift": -8414.46, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9999940660408995}}]