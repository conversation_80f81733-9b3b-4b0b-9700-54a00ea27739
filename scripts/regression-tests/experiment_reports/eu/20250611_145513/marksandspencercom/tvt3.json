[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-07-31", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "marksandspencercom", "startDate": "2024-07-18"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3822008, "name": "Taggstar Experiment Sessions", "total": 7651346, "treatment": 3829338, "type": "long"}, {"adjusted": null, "control": 0.4995, "name": "Experiment split", "total": null, "treatment": 0.5005, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 18674830, "name": "Taggstar Experiment Impressions", "total": 37359314, "treatment": 18684484, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 16067859, "name": "Taggstar Message Impressions", "total": 31240922, "treatment": 15173063, "type": "long"}, {"adjusted": null, "control": 0.8604, "name": "Taggstar coverage", "total": null, "treatment": 0.8121, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 419838, "control": 209685, "name": "Number of orders", "total": 419805, "treatment": 210120, "type": "long"}, {"adjusted": 66, "control": null, "name": "Uplift", "total": null, "treatment": 33, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0549, "control": 0.0549, "name": "Conversion rate", "total": 0.0549, "treatment": 0.0549, "type": "percentage"}, {"adjusted": 0.0002, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0002, "type": "percentage"}, {"adjusted": null, "control": 0.0546, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1093, "treatment": 0.0546, "type": "percentage"}, {"adjusted": null, "control": 0.0551, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1102, "treatment": 0.0551, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0081, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0085, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 25412946, "control": 12653282, "name": "Revenue", "total": 25371928, "treatment": 12718646, "type": "currency"}, {"adjusted": null, "control": 60.34, "name": "AOV", "total": 60.44, "treatment": 60.53, "type": "currency"}, {"adjusted": 82114, "control": null, "name": "Uplift", "total": null, "treatment": 41097, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0002, "actualQuantityUplift": 32.86, "adjustedQuantityUplift": 65.65, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.4792190938614995}}]