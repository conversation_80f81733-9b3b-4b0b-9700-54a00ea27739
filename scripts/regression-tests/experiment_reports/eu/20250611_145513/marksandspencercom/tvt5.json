[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-10-03", "experimentId": "tvt5", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "marksandspencercom", "startDate": "2024-08-30"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 12539887, "name": "Taggstar Experiment Sessions", "total": 25078902, "treatment": 12539015, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 60673815, "name": "Taggstar Experiment Impressions", "total": 121383586, "treatment": 60709771, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 53052404, "name": "Taggstar Message Impressions", "total": 104831620, "treatment": 51779216, "type": "long"}, {"adjusted": null, "control": 0.8744, "name": "Taggstar coverage", "total": null, "treatment": 0.8529, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 1315218, "control": 657135, "name": "Number of orders", "total": 1314721, "treatment": 657586, "type": "long"}, {"adjusted": 993, "control": null, "name": "Uplift", "total": null, "treatment": 497, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0524, "control": 0.0524, "name": "Conversion rate", "total": 0.0524, "treatment": 0.0524, "type": "percentage"}, {"adjusted": 0.0008, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0008, "type": "percentage"}, {"adjusted": null, "control": 0.0523, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1046, "treatment": 0.0523, "type": "percentage"}, {"adjusted": null, "control": 0.0525, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1051, "treatment": 0.0526, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0039, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0055, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 77413112, "control": 38684171, "name": "Revenue", "total": 77389382, "treatment": 38705210, "type": "currency"}, {"adjusted": null, "control": 58.87, "name": "AOV", "total": 58.86, "treatment": 58.86, "type": "currency"}, {"adjusted": 47459, "control": null, "name": "Uplift", "total": null, "treatment": 23729, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0008, "actualQuantityUplift": 496.7, "adjustedQuantityUplift": 993.43, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.32815183703914996}}]