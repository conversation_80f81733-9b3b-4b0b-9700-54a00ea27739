[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-11-02", "experimentId": "TvC1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "shoptottenhamhotspurcom", "startDate": "2023-08-04"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1463013, "name": "Taggstar Experiment Sessions", "total": 2922713, "treatment": 1459700, "type": "long"}, {"adjusted": null, "control": 0.5006, "name": "Experiment split", "total": null, "treatment": 0.4994, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5856667, "name": "Taggstar Experiment Impressions", "total": 11712185, "treatment": 5855518, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 4917349, "treatment": 4917349, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8398, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 76929, "control": 37490, "name": "Number of orders", "total": 75911, "treatment": 38421, "type": "long"}, {"adjusted": 2034, "control": null, "name": "Uplift", "total": null, "treatment": 1016, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0263, "control": 0.0256, "name": "Conversion rate", "total": 0.026, "treatment": 0.0263, "type": "percentage"}, {"adjusted": 0.0272, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0272, "type": "percentage"}, {"adjusted": null, "control": 0.0254, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0514, "treatment": 0.0261, "type": "percentage"}, {"adjusted": null, "control": 0.0259, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0525, "treatment": 0.0266, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.007, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0478, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 6406019, "control": 3145558, "name": "Revenue", "total": 6344937, "treatment": 3199379, "type": "currency"}, {"adjusted": null, "control": 83.9, "name": "AOV", "total": 83.58, "treatment": 83.27, "type": "currency"}, {"adjusted": 122027, "control": null, "name": "Uplift", "total": null, "treatment": 60944, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0272, "actualQuantityUplift": 1015.9, "adjustedQuantityUplift": 2034.1, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 9.192575231402572e-05}}]