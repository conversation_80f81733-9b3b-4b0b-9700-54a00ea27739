[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-10-16", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "shoptottenhamhotspurcom", "startDate": "2024-08-30"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 619408, "name": "Taggstar Experiment Sessions", "total": 1239466, "treatment": 620058, "type": "long"}, {"adjusted": null, "control": 0.4997, "name": "Experiment split", "total": null, "treatment": 0.5003, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2370174, "name": "Taggstar Experiment Impressions", "total": 4756655, "treatment": 2386481, "type": "long"}, {"adjusted": null, "control": 0.4983, "name": "Experiment split", "total": null, "treatment": 0.5017, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1980753, "name": "Taggstar Message Impressions", "total": 4036904, "treatment": 2056151, "type": "long"}, {"adjusted": null, "control": 0.8357, "name": "Taggstar coverage", "total": null, "treatment": 0.8616, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 31839, "control": 16038, "name": "Number of orders", "total": 31966, "treatment": 15928, "type": "long"}, {"adjusted": -254, "control": null, "name": "Uplift", "total": null, "treatment": -127, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0257, "control": 0.0259, "name": "Conversion rate", "total": 0.0258, "treatment": 0.0257, "type": "percentage"}, {"adjusted": -0.0079, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0079, "type": "percentage"}, {"adjusted": null, "control": 0.0255, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0508, "treatment": 0.0253, "type": "percentage"}, {"adjusted": null, "control": 0.0263, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0524, "treatment": 0.0261, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0378, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0229, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2635331, "control": 1339245, "name": "Revenue", "total": 2657602, "treatment": 1318356, "type": "currency"}, {"adjusted": null, "control": 83.5, "name": "AOV", "total": 83.14, "treatment": 82.77, "type": "currency"}, {"adjusted": -44565, "control": null, "name": "Uplift", "total": null, "treatment": -22294, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0079, "actualQuantityUplift": -126.83, "adjustedQuantityUplift": -253.53, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7637227699893593}}]