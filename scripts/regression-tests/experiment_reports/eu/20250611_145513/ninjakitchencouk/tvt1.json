[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-06-24", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "ninja<PERSON>tchen<PERSON>uk", "startDate": "2024-04-25"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1934864, "name": "Taggstar Experiment Sessions", "total": 3872894, "treatment": 1938030, "type": "long"}, {"adjusted": null, "control": 0.4996, "name": "Experiment split", "total": null, "treatment": 0.5004, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4402098, "name": "Taggstar Experiment Impressions", "total": 8808716, "treatment": 4406618, "type": "long"}, {"adjusted": null, "control": 0.4997, "name": "Experiment split", "total": null, "treatment": 0.5003, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 4275012, "name": "Taggstar Message Impressions", "total": 8629085, "treatment": 4354073, "type": "long"}, {"adjusted": null, "control": 0.9711, "name": "Taggstar coverage", "total": null, "treatment": 0.9881, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 136474, "control": 68467, "name": "Number of orders", "total": 136760, "treatment": 68293, "type": "long"}, {"adjusted": -572, "control": null, "name": "Uplift", "total": null, "treatment": -286, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0352, "control": 0.0354, "name": "Conversion rate", "total": 0.0353, "treatment": 0.0352, "type": "percentage"}, {"adjusted": -0.0042, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0042, "type": "percentage"}, {"adjusted": null, "control": 0.0351, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0701, "treatment": 0.035, "type": "percentage"}, {"adjusted": null, "control": 0.0356, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0711, "treatment": 0.0355, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0187, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0106, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 22390308, "control": 11269736, "name": "Revenue", "total": 22474042, "treatment": 11204306, "type": "currency"}, {"adjusted": null, "control": 164.6, "name": "AOV", "total": 164.33, "treatment": 164.06, "type": "currency"}, {"adjusted": -167605, "control": null, "name": "Uplift", "total": null, "treatment": -83871, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0042, "actualQuantityUplift": -286.03, "adjustedQuantityUplift": -571.6, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7843119132791272}}]