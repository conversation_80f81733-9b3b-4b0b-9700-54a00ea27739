[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-06-03", "experimentId": "tvc2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "ninja<PERSON>tchen<PERSON>uk", "startDate": "2025-03-17"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3023050, "name": "Taggstar Experiment Sessions", "total": 6041919, "treatment": 3018869, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6252249, "name": "Taggstar Experiment Impressions", "total": 12505971, "treatment": 6253722, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 6086522, "treatment": 6086522, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9733, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 143021, "control": 69836, "name": "Number of orders", "total": 141297, "treatment": 71461, "type": "long"}, {"adjusted": 3446, "control": null, "name": "Uplift", "total": null, "treatment": 1722, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0237, "control": 0.0231, "name": "Conversion rate", "total": 0.0234, "treatment": 0.0237, "type": "percentage"}, {"adjusted": 0.0247, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0247, "type": "percentage"}, {"adjusted": null, "control": 0.0229, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0464, "treatment": 0.0235, "type": "percentage"}, {"adjusted": null, "control": 0.0233, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0471, "treatment": 0.0238, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0099, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0397, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 26150821, "control": 12486491, "name": "Revenue", "total": 25552853, "treatment": 13066362, "type": "currency"}, {"adjusted": null, "control": 178.8, "name": "AOV", "total": 180.84, "treatment": 182.85, "type": "currency"}, {"adjusted": 1195108, "control": null, "name": "Uplift", "total": null, "treatment": 597141, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0247, "actualQuantityUplift": 1721.59, "adjustedQuantityUplift": 3445.56, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.7619130568880913e-06}}]