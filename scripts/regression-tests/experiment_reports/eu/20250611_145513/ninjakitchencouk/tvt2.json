[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-07-24", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "ninja<PERSON>tchen<PERSON>uk", "startDate": "2024-07-02"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 747890, "name": "Taggstar Experiment Sessions", "total": 1496380, "treatment": 748490, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1740941, "name": "Taggstar Experiment Impressions", "total": 3477397, "treatment": 1736456, "type": "long"}, {"adjusted": null, "control": 0.5006, "name": "Experiment split", "total": null, "treatment": 0.4994, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1689127, "name": "Taggstar Message Impressions", "total": 3408508, "treatment": 1719381, "type": "long"}, {"adjusted": null, "control": 0.9702, "name": "Taggstar coverage", "total": null, "treatment": 0.9902, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 51521, "control": 26344, "name": "Number of orders", "total": 52115, "treatment": 25771, "type": "long"}, {"adjusted": -1188, "control": null, "name": "Uplift", "total": null, "treatment": -594, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0344, "control": 0.0352, "name": "Conversion rate", "total": 0.0348, "treatment": 0.0344, "type": "percentage"}, {"adjusted": -0.0225, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0225, "type": "percentage"}, {"adjusted": null, "control": 0.0348, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0688, "treatment": 0.034, "type": "percentage"}, {"adjusted": null, "control": 0.0356, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0705, "treatment": 0.0348, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0456, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0011, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 8154387, "control": 4164155, "name": "Revenue", "total": 8242983, "treatment": 4078828, "type": "currency"}, {"adjusted": null, "control": 158.07, "name": "AOV", "total": 158.17, "treatment": 158.27, "type": "currency"}, {"adjusted": -177263, "control": null, "name": "Uplift", "total": null, "treatment": -88667, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0225, "actualQuantityUplift": -594.13, "adjustedQuantityUplift": -1187.79, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9959521083323928}}]