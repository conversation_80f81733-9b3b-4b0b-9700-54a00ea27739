[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2021-01-18", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "bestwaywholesalecouk", "startDate": "2020-10-01"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 29305, "name": "Taggstar Experiment Sessions", "total": 603258, "treatment": 573953, "type": "long"}, {"adjusted": null, "control": 0.0486, "name": "Experiment split", "total": null, "treatment": 0.9514, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 239277, "name": "Taggstar Experiment Impressions", "total": 4630499, "treatment": 4391222, "type": "long"}, {"adjusted": null, "control": 0.0517, "name": "Experiment split", "total": null, "treatment": 0.9483, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 2557862, "treatment": 2557862, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.5825, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 23134, "control": 1234, "name": "Number of orders", "total": 23244, "treatment": 22010, "type": "long"}, {"adjusted": -2269, "control": null, "name": "Uplift", "total": null, "treatment": -2159, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0383, "control": 0.0421, "name": "Conversion rate", "total": 0.0402, "treatment": 0.0383, "type": "percentage"}, {"adjusted": -0.0893, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0893, "type": "percentage"}, {"adjusted": null, "control": 0.0398, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0777, "treatment": 0.0379, "type": "percentage"}, {"adjusted": null, "control": 0.0444, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0833, "treatment": 0.0388, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.1477, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": -0.0242, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 15053781, "control": 793313, "name": "Revenue", "total": 15115814, "treatment": 14322500, "type": "currency"}, {"adjusted": null, "control": 642.88, "name": "AOV", "total": 650.31, "treatment": 650.73, "type": "currency"}, {"adjusted": -1276968, "control": null, "name": "Uplift", "total": null, "treatment": -1214936, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0893, "actualQuantityUplift": -2158.5, "adjustedQuantityUplift": -2268.71, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9991355975359263}}]