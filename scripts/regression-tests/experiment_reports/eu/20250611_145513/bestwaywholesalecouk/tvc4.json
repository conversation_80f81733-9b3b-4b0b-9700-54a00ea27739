[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-09-25", "experimentId": "tvc4", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "bestwaywholesalecouk", "startDate": "2023-08-26"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 141250, "name": "Taggstar Experiment Sessions", "total": 283396, "treatment": 142146, "type": "long"}, {"adjusted": null, "control": 0.4984, "name": "Experiment split", "total": null, "treatment": 0.5016, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 518849, "name": "Taggstar Experiment Impressions", "total": 1036766, "treatment": 517917, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 240036, "treatment": 240036, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.4635, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 3842, "control": 1761, "name": "Number of orders", "total": 3688, "treatment": 1927, "type": "long"}, {"adjusted": 309, "control": null, "name": "Uplift", "total": null, "treatment": 155, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0136, "control": 0.0125, "name": "Conversion rate", "total": 0.013, "treatment": 0.0136, "type": "percentage"}, {"adjusted": 0.0874, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0874, "type": "percentage"}, {"adjusted": null, "control": 0.0119, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0248, "treatment": 0.013, "type": "percentage"}, {"adjusted": null, "control": 0.013, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0272, "treatment": 0.0142, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0069, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.1909, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2651109, "control": 1240613, "name": "Revenue", "total": 2570359, "treatment": 1329745, "type": "currency"}, {"adjusted": null, "control": 704.49, "name": "AOV", "total": 696.95, "treatment": 690.06, "type": "currency"}, {"adjusted": 162013, "control": null, "name": "Uplift", "total": null, "treatment": 81262, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0874, "actualQuantityUplift": 154.83, "adjustedQuantityUplift": 308.68, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.005255757494574809}}]