[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2021-10-11", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "bestwaywholesalecouk", "startDate": "2021-06-04"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 620180, "name": "Taggstar Experiment Sessions", "total": 1228589, "treatment": 608409, "type": "long"}, {"adjusted": null, "control": 0.5048, "name": "Experiment split", "total": null, "treatment": 0.4952, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2606537, "name": "Taggstar Experiment Impressions", "total": 5100805, "treatment": 2494268, "type": "long"}, {"adjusted": null, "control": 0.511, "name": "Experiment split", "total": null, "treatment": 0.489, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 424253, "name": "Taggstar Message Impressions", "total": 1842868, "treatment": 1418615, "type": "long"}, {"adjusted": null, "control": 0.1628, "name": "Taggstar coverage", "total": null, "treatment": 0.5688, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 25244, "control": 13033, "name": "Number of orders", "total": 25534, "treatment": 12501, "type": "long"}, {"adjusted": -575, "control": null, "name": "Uplift", "total": null, "treatment": -285, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0205, "control": 0.021, "name": "Conversion rate", "total": 0.0208, "treatment": 0.0205, "type": "percentage"}, {"adjusted": -0.0223, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0223, "type": "percentage"}, {"adjusted": null, "control": 0.0207, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0408, "treatment": 0.0202, "type": "percentage"}, {"adjusted": null, "control": 0.0214, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0423, "treatment": 0.0209, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0553, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0119, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 15787972, "control": 8659722, "name": "Revenue", "total": 16478077, "treatment": 7818355, "type": "currency"}, {"adjusted": null, "control": 664.45, "name": "AOV", "total": 645.34, "treatment": 625.42, "type": "currency"}, {"adjusted": -1367110, "control": null, "name": "Uplift", "total": null, "treatment": -677006, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0223, "actualQuantityUplift": -284.63, "adjustedQuantityUplift": -574.77, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9654364267119943}}]