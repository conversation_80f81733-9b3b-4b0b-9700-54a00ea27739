[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-09-16", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "bestwaywholesalecouk", "startDate": "2024-07-20"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 150481, "name": "Taggstar Experiment Sessions", "total": 300072, "treatment": 149591, "type": "long"}, {"adjusted": null, "control": 0.5015, "name": "Experiment split", "total": null, "treatment": 0.4985, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 866084, "name": "Taggstar Experiment Impressions", "total": 1663061, "treatment": 796977, "type": "long"}, {"adjusted": null, "control": 0.5208, "name": "Experiment split", "total": null, "treatment": 0.4792, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 576521, "name": "Taggstar Message Impressions", "total": 1112255, "treatment": 535734, "type": "long"}, {"adjusted": null, "control": 0.6657, "name": "Taggstar coverage", "total": null, "treatment": 0.6722, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 6313, "control": 3149, "name": "Number of orders", "total": 6296, "treatment": 3147, "type": "long"}, {"adjusted": 33, "control": null, "name": "Uplift", "total": null, "treatment": 17, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.021, "control": 0.0209, "name": "Conversion rate", "total": 0.021, "treatment": 0.021, "type": "percentage"}, {"adjusted": 0.0053, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0053, "type": "percentage"}, {"adjusted": null, "control": 0.0202, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0405, "treatment": 0.0203, "type": "percentage"}, {"adjusted": null, "control": 0.0216, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0434, "treatment": 0.0218, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0619, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0773, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 4197524, "control": 2109621, "name": "Revenue", "total": 4202158, "treatment": 2092537, "type": "currency"}, {"adjusted": null, "control": 669.93, "name": "AOV", "total": 667.43, "treatment": 664.93, "type": "currency"}, {"adjusted": -9241, "control": null, "name": "Uplift", "total": null, "treatment": -4607, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0053, "actualQuantityUplift": 16.62, "adjustedQuantityUplift": 33.35, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.41590759554088236}}]