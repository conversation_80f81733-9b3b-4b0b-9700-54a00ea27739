[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-10-02", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "fashionworldcouk", "startDate": "2024-08-29"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 210386, "name": "Taggstar Experiment Sessions", "total": 421995, "treatment": 211609, "type": "long"}, {"adjusted": null, "control": 0.4986, "name": "Experiment split", "total": null, "treatment": 0.5014, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1978576, "name": "Taggstar Experiment Impressions", "total": 3953710, "treatment": 1975134, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1524945, "name": "Taggstar Message Impressions", "total": 3047478, "treatment": 1522533, "type": "long"}, {"adjusted": null, "control": 0.7707, "name": "Taggstar coverage", "total": null, "treatment": 0.7709, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 34201, "control": 16840, "name": "Number of orders", "total": 33990, "treatment": 17150, "type": "long"}, {"adjusted": 423, "control": null, "name": "Uplift", "total": null, "treatment": 212, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.081, "control": 0.08, "name": "Conversion rate", "total": 0.0805, "treatment": 0.081, "type": "percentage"}, {"adjusted": 0.0125, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0125, "type": "percentage"}, {"adjusted": null, "control": 0.0789, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1588, "treatment": 0.0799, "type": "percentage"}, {"adjusted": null, "control": 0.0812, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1634, "treatment": 0.0822, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0163, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0421, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3788760, "control": 1840426, "name": "Revenue", "total": 3740297, "treatment": 1899870, "type": "currency"}, {"adjusted": null, "control": 109.29, "name": "AOV", "total": 110.04, "treatment": 110.78, "type": "currency"}, {"adjusted": 97209, "control": null, "name": "Uplift", "total": null, "treatment": 48745, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0125, "actualQuantityUplift": 212.11, "adjustedQuantityUplift": 422.99, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.1157771237273783}}]