[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-02-17", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "fashionworldcouk", "startDate": "2023-01-25"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 114689, "name": "Taggstar Experiment Sessions", "total": 228028, "treatment": 113339, "type": "long"}, {"adjusted": null, "control": 0.503, "name": "Experiment split", "total": null, "treatment": 0.497, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1286618, "name": "Taggstar Experiment Impressions", "total": 2548389, "treatment": 1261771, "type": "long"}, {"adjusted": null, "control": 0.5049, "name": "Experiment split", "total": null, "treatment": 0.4951, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 87378, "name": "Taggstar Message Impressions", "total": 651222, "treatment": 563844, "type": "long"}, {"adjusted": null, "control": 0.0679, "name": "Taggstar coverage", "total": null, "treatment": 0.4469, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 17630, "control": 8821, "name": "Number of orders", "total": 17584, "treatment": 8763, "type": "long"}, {"adjusted": 92, "control": null, "name": "Uplift", "total": null, "treatment": 46, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0773, "control": 0.0769, "name": "Conversion rate", "total": 0.0771, "treatment": 0.0773, "type": "percentage"}, {"adjusted": 0.0053, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0053, "type": "percentage"}, {"adjusted": null, "control": 0.0754, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1511, "treatment": 0.0758, "type": "percentage"}, {"adjusted": null, "control": 0.0785, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1573, "treatment": 0.0789, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0343, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0465, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1601632, "control": 779312, "name": "Revenue", "total": 1575387, "treatment": 796075, "type": "currency"}, {"adjusted": null, "control": 88.35, "name": "AOV", "total": 89.59, "treatment": 90.85, "type": "currency"}, {"adjusted": 52181, "control": null, "name": "Uplift", "total": null, "treatment": 25936, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0053, "actualQuantityUplift": 45.83, "adjustedQuantityUplift": 92.21, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.35871052005229415}}]