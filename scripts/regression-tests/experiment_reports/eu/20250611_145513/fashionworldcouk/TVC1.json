[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-04-18", "experimentId": "TVC1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "fashionworldcouk", "startDate": "2023-05-05"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 156376, "name": "Taggstar Experiment Sessions", "total": 3104416, "treatment": 2948040, "type": "long"}, {"adjusted": null, "control": 0.0504, "name": "Experiment split", "total": null, "treatment": 0.9496, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 666402, "name": "Taggstar Experiment Impressions", "total": 13257378, "treatment": 12590976, "type": "long"}, {"adjusted": null, "control": 0.0503, "name": "Experiment split", "total": null, "treatment": 0.9497, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 6474271, "treatment": 6474271, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.5142, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 308365, "control": 15678, "name": "Number of orders", "total": 308510, "treatment": 292832, "type": "long"}, {"adjusted": -2879, "control": null, "name": "Uplift", "total": null, "treatment": -2734, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0993, "control": 0.1003, "name": "Conversion rate", "total": 0.0998, "treatment": 0.0993, "type": "percentage"}, {"adjusted": -0.0092, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0092, "type": "percentage"}, {"adjusted": null, "control": 0.0988, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1978, "treatment": 0.099, "type": "percentage"}, {"adjusted": null, "control": 0.1017, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.2014, "treatment": 0.0997, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0271, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0091, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 29713789, "control": 1499625, "name": "Revenue", "total": 29716668, "treatment": 28217043, "type": "currency"}, {"adjusted": null, "control": 95.65, "name": "AOV", "total": 96.32, "treatment": 96.36, "type": "currency"}, {"adjusted": -57146, "control": null, "name": "Uplift", "total": null, "treatment": -54268, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0092, "actualQuantityUplift": -2733.63, "adjustedQuantityUplift": -2878.63, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8829725859082336}}]