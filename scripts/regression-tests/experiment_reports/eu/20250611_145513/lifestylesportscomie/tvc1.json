[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2021-11-09", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2021-05-28"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 831340, "name": "Taggstar Experiment Sessions", "total": 1663990, "treatment": 832650, "type": "long"}, {"adjusted": null, "control": 0.4996, "name": "Experiment split", "total": null, "treatment": 0.5004, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3361528, "name": "Taggstar Experiment Impressions", "total": 6750665, "treatment": 3389137, "type": "long"}, {"adjusted": null, "control": 0.498, "name": "Experiment split", "total": null, "treatment": 0.502, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 2740084, "treatment": 2740084, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8085, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 62045, "control": 30162, "name": "Number of orders", "total": 61209, "treatment": 31047, "type": "long"}, {"adjusted": 1674, "control": null, "name": "Uplift", "total": null, "treatment": 837, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0373, "control": 0.0363, "name": "Conversion rate", "total": 0.0368, "treatment": 0.0373, "type": "percentage"}, {"adjusted": 0.0277, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0277, "type": "percentage"}, {"adjusted": null, "control": 0.0359, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0728, "treatment": 0.0369, "type": "percentage"}, {"adjusted": null, "control": 0.0367, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0744, "treatment": 0.0377, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0054, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0506, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 4200619, "control": 2052596, "name": "Revenue", "total": 4154559, "treatment": 2101963, "type": "currency"}, {"adjusted": null, "control": 68.05, "name": "AOV", "total": 67.87, "treatment": 67.7, "type": "currency"}, {"adjusted": 92193, "control": null, "name": "Uplift", "total": null, "treatment": 46133, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0277, "actualQuantityUplift": 837.47, "adjustedQuantityUplift": 1673.63, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0002840611131558728}}]