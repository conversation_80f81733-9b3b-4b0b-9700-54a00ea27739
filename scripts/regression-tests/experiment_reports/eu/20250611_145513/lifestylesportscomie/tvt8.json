[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-08-07", "experimentId": "tvt8", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2024-07-02"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 588488, "name": "Taggstar Experiment Sessions", "total": 1174779, "treatment": 586291, "type": "long"}, {"adjusted": null, "control": 0.5009, "name": "Experiment split", "total": null, "treatment": 0.4991, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2019183, "name": "Taggstar Experiment Impressions", "total": 4035370, "treatment": 2016187, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1746333, "name": "Taggstar Message Impressions", "total": 3489817, "treatment": 1743484, "type": "long"}, {"adjusted": null, "control": 0.8649, "name": "Taggstar coverage", "total": null, "treatment": 0.8647, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 36913, "control": 18646, "name": "Number of orders", "total": 37068, "treatment": 18422, "type": "long"}, {"adjusted": -309, "control": null, "name": "Uplift", "total": null, "treatment": -154, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0314, "control": 0.0317, "name": "Conversion rate", "total": 0.0316, "treatment": 0.0314, "type": "percentage"}, {"adjusted": -0.0083, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0083, "type": "percentage"}, {"adjusted": null, "control": 0.0312, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0622, "treatment": 0.031, "type": "percentage"}, {"adjusted": null, "control": 0.0321, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.064, "treatment": 0.0319, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.036, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0202, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3137229, "control": 1594431, "name": "Revenue", "total": 3160112, "treatment": 1565681, "type": "currency"}, {"adjusted": null, "control": 85.51, "name": "AOV", "total": 85.25, "treatment": 84.99, "type": "currency"}, {"adjusted": -45682, "control": null, "name": "Uplift", "total": null, "treatment": -22798, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0083, "actualQuantityUplift": -154.39, "adjustedQuantityUplift": -309.36, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7928596638018655}}]