[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-09-25", "experimentId": "tvc2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2023-08-19"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 409819, "name": "Taggstar Experiment Sessions", "total": 823593, "treatment": 413774, "type": "long"}, {"adjusted": null, "control": 0.4976, "name": "Experiment split", "total": null, "treatment": 0.5024, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1559656, "name": "Taggstar Experiment Impressions", "total": 3144824, "treatment": 1585168, "type": "long"}, {"adjusted": null, "control": 0.4959, "name": "Experiment split", "total": null, "treatment": 0.5041, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 1276595, "treatment": 1276595, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8053, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 32645, "control": 15575, "name": "Number of orders", "total": 31976, "treatment": 16401, "type": "long"}, {"adjusted": 1345, "control": null, "name": "Uplift", "total": null, "treatment": 676, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0396, "control": 0.038, "name": "Conversion rate", "total": 0.0388, "treatment": 0.0396, "type": "percentage"}, {"adjusted": 0.043, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.043, "type": "percentage"}, {"adjusted": null, "control": 0.0374, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0765, "treatment": 0.039, "type": "percentage"}, {"adjusted": null, "control": 0.0386, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0788, "treatment": 0.0402, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0117, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0752, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2902149, "control": 1377387, "name": "Revenue", "total": 2835430, "treatment": 1458043, "type": "currency"}, {"adjusted": null, "control": 88.44, "name": "AOV", "total": 88.67, "treatment": 88.9, "type": "currency"}, {"adjusted": 134082, "control": null, "name": "Uplift", "total": null, "treatment": 67363, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.043, "actualQuantityUplift": 675.69, "adjustedQuantityUplift": 1344.93, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 6.248851947829128e-05}}]