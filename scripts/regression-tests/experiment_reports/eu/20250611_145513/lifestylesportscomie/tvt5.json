[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-02-28", "experimentId": "tvt5", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2024-01-27"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 348178, "name": "Taggstar Experiment Sessions", "total": 695421, "treatment": 347243, "type": "long"}, {"adjusted": null, "control": 0.5007, "name": "Experiment split", "total": null, "treatment": 0.4993, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1356465, "name": "Taggstar Experiment Impressions", "total": 2710033, "treatment": 1353568, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1104959, "name": "Taggstar Message Impressions", "total": 2158703, "treatment": 1053744, "type": "long"}, {"adjusted": null, "control": 0.8146, "name": "Taggstar coverage", "total": null, "treatment": 0.7785, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 21579, "control": 11057, "name": "Number of orders", "total": 21832, "treatment": 10775, "type": "long"}, {"adjusted": -505, "control": null, "name": "Uplift", "total": null, "treatment": -252, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.031, "control": 0.0318, "name": "Conversion rate", "total": 0.0314, "treatment": 0.031, "type": "percentage"}, {"adjusted": -0.0229, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0229, "type": "percentage"}, {"adjusted": null, "control": 0.0312, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0616, "treatment": 0.0305, "type": "percentage"}, {"adjusted": null, "control": 0.0323, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0639, "treatment": 0.0316, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0583, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0139, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1921892, "control": 996466, "name": "Revenue", "total": 1956120, "treatment": 959654, "type": "currency"}, {"adjusted": null, "control": 90.12, "name": "AOV", "total": 89.6, "treatment": 89.06, "type": "currency"}, {"adjusted": -68365, "control": null, "name": "Uplift", "total": null, "treatment": -34137, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0229, "actualQuantityUplift": -252.31, "adjustedQuantityUplift": -505.29, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9588421580741453}}]