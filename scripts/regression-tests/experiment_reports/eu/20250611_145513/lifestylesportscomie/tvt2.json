[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-04-27", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2023-04-13"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 228389, "name": "Taggstar Experiment Sessions", "total": 457733, "treatment": 229344, "type": "long"}, {"adjusted": null, "control": 0.499, "name": "Experiment split", "total": null, "treatment": 0.501, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 900092, "name": "Taggstar Experiment Impressions", "total": 1804013, "treatment": 903921, "type": "long"}, {"adjusted": null, "control": 0.4989, "name": "Experiment split", "total": null, "treatment": 0.5011, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 660600, "name": "Taggstar Message Impressions", "total": 1174574, "treatment": 513974, "type": "long"}, {"adjusted": null, "control": 0.7339, "name": "Taggstar coverage", "total": null, "treatment": 0.5686, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 16999, "control": 8654, "name": "Number of orders", "total": 17171, "treatment": 8517, "type": "long"}, {"adjusted": -346, "control": null, "name": "Uplift", "total": null, "treatment": -173, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0371, "control": 0.0379, "name": "Conversion rate", "total": 0.0375, "treatment": 0.0371, "type": "percentage"}, {"adjusted": -0.0199, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0199, "type": "percentage"}, {"adjusted": null, "control": 0.0371, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0735, "treatment": 0.0364, "type": "percentage"}, {"adjusted": null, "control": 0.0387, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0766, "treatment": 0.0379, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0598, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0216, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1419606, "control": 728648, "name": "Revenue", "total": 1439932, "treatment": 711284, "type": "currency"}, {"adjusted": null, "control": 84.2, "name": "AOV", "total": 83.86, "treatment": 83.51, "type": "currency"}, {"adjusted": -40736, "control": null, "name": "Uplift", "total": null, "treatment": -20410, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0199, "actualQuantityUplift": -173.19, "adjustedQuantityUplift": -345.65, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.910578462852834}}]