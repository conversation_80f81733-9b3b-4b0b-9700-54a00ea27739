[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-01-26", "experimentId": "tvt4", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2023-12-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 595038, "name": "Taggstar Experiment Sessions", "total": 1189326, "treatment": 594288, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2769783, "name": "Taggstar Experiment Impressions", "total": 5517958, "treatment": 2748175, "type": "long"}, {"adjusted": null, "control": 0.502, "name": "Experiment split", "total": null, "treatment": 0.498, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 2129272, "name": "Taggstar Message Impressions", "total": 4500888, "treatment": 2371616, "type": "long"}, {"adjusted": null, "control": 0.7688, "name": "Taggstar coverage", "total": null, "treatment": 0.863, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 48196, "control": 24234, "name": "Number of orders", "total": 48317, "treatment": 24083, "type": "long"}, {"adjusted": -241, "control": null, "name": "Uplift", "total": null, "treatment": -120, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0405, "control": 0.0407, "name": "Conversion rate", "total": 0.0406, "treatment": 0.0405, "type": "percentage"}, {"adjusted": -0.005, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.005, "type": "percentage"}, {"adjusted": null, "control": 0.0402, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0802, "treatment": 0.04, "type": "percentage"}, {"adjusted": null, "control": 0.0412, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0823, "treatment": 0.041, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0293, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0199, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 4323404, "control": 2171009, "name": "Revenue", "total": 4331348, "treatment": 2160339, "type": "currency"}, {"adjusted": null, "control": 89.59, "name": "AOV", "total": 89.64, "treatment": 89.7, "type": "currency"}, {"adjusted": -15879, "control": null, "name": "Uplift", "total": null, "treatment": -7934, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.005, "actualQuantityUplift": -120.45, "adjustedQuantityUplift": -241.06, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7122017718870046}}]