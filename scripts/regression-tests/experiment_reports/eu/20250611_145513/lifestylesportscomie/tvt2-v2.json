[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-07-19", "experimentId": "tvt2-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2023-04-28"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1042997, "name": "Taggstar Experiment Sessions", "total": 2085409, "treatment": 1042412, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4622848, "name": "Taggstar Experiment Impressions", "total": 9227874, "treatment": 4605026, "type": "long"}, {"adjusted": null, "control": 0.501, "name": "Experiment split", "total": null, "treatment": 0.499, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 3561408, "name": "Taggstar Message Impressions", "total": 7077598, "treatment": 3516190, "type": "long"}, {"adjusted": null, "control": 0.7704, "name": "Taggstar coverage", "total": null, "treatment": 0.7636, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 82903, "control": 42662, "name": "Number of orders", "total": 84102, "treatment": 41440, "type": "long"}, {"adjusted": -2397, "control": null, "name": "Uplift", "total": null, "treatment": -1198, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0398, "control": 0.0409, "name": "Conversion rate", "total": 0.0403, "treatment": 0.0398, "type": "percentage"}, {"adjusted": -0.0281, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0281, "type": "percentage"}, {"adjusted": null, "control": 0.0405, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0799, "treatment": 0.0394, "type": "percentage"}, {"adjusted": null, "control": 0.0413, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0814, "treatment": 0.0401, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0461, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": -0.0097, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 6876684, "control": 3540081, "name": "Revenue", "total": 6977459, "treatment": 3437378, "type": "currency"}, {"adjusted": null, "control": 82.98, "name": "AOV", "total": 82.96, "treatment": 82.95, "type": "currency"}, {"adjusted": -201493, "control": null, "name": "Uplift", "total": null, "treatment": -100718, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0281, "actualQuantityUplift": -1198.07, "adjustedQuantityUplift": -2396.82, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.999987695754156}}]