[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-04-30", "experimentId": "tvt6", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2024-03-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 630075, "name": "Taggstar Experiment Sessions", "total": 1259796, "treatment": 629721, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2278008, "name": "Taggstar Experiment Impressions", "total": 4561837, "treatment": 2283829, "type": "long"}, {"adjusted": null, "control": 0.4994, "name": "Experiment split", "total": null, "treatment": 0.5006, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1912593, "name": "Taggstar Message Impressions", "total": 3753827, "treatment": 1841234, "type": "long"}, {"adjusted": null, "control": 0.8396, "name": "Taggstar coverage", "total": null, "treatment": 0.8062, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 41252, "control": 20541, "name": "Number of orders", "total": 41161, "treatment": 20620, "type": "long"}, {"adjusted": 181, "control": null, "name": "Uplift", "total": null, "treatment": 91, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0327, "control": 0.0326, "name": "Conversion rate", "total": 0.0327, "treatment": 0.0327, "type": "percentage"}, {"adjusted": 0.0044, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0044, "type": "percentage"}, {"adjusted": null, "control": 0.0322, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0645, "treatment": 0.0323, "type": "percentage"}, {"adjusted": null, "control": 0.033, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0662, "treatment": 0.0332, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0222, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0318, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3703546, "control": 1833969, "name": "Revenue", "total": 3685221, "treatment": 1851253, "type": "currency"}, {"adjusted": null, "control": 89.28, "name": "AOV", "total": 89.53, "treatment": 89.78, "type": "currency"}, {"adjusted": 36640, "control": null, "name": "Uplift", "total": null, "treatment": 18315, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0044, "actualQuantityUplift": 90.54, "adjustedQuantityUplift": 181.13, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.32495948008265274}}]