[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-09-29", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2022-04-12"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2560909, "name": "Taggstar Experiment Sessions", "total": 5123998, "treatment": 2563089, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 10447171, "name": "Taggstar Experiment Impressions", "total": 20895612, "treatment": 10448441, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 7968410, "name": "Taggstar Message Impressions", "total": 15958705, "treatment": 7990295, "type": "long"}, {"adjusted": null, "control": 0.7627, "name": "Taggstar coverage", "total": null, "treatment": 0.7647, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 173000, "control": 86273, "name": "Number of orders", "total": 172810, "treatment": 86537, "type": "long"}, {"adjusted": 381, "control": null, "name": "Uplift", "total": null, "treatment": 191, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0338, "control": 0.0337, "name": "Conversion rate", "total": 0.0337, "treatment": 0.0338, "type": "percentage"}, {"adjusted": 0.0022, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0022, "type": "percentage"}, {"adjusted": null, "control": 0.0335, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.067, "treatment": 0.0335, "type": "percentage"}, {"adjusted": null, "control": 0.0339, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0679, "treatment": 0.034, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0108, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0154, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 13019500, "control": 6551580, "name": "Revenue", "total": 13064100, "treatment": 6512520, "type": "currency"}, {"adjusted": null, "control": 75.94, "name": "AOV", "total": 75.6, "treatment": 75.26, "type": "currency"}, {"adjusted": -89238, "control": null, "name": "Uplift", "total": null, "treatment": -44638, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0022, "actualQuantityUplift": 190.56, "adjustedQuantityUplift": 380.96, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.3205598071903369}}]