[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-06-12", "experimentId": "tvt7", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2024-05-14"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 390368, "name": "Taggstar Experiment Sessions", "total": 778452, "treatment": 388084, "type": "long"}, {"adjusted": null, "control": 0.5015, "name": "Experiment split", "total": null, "treatment": 0.4985, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1608067, "name": "Taggstar Experiment Impressions", "total": 3194018, "treatment": 1585951, "type": "long"}, {"adjusted": null, "control": 0.5035, "name": "Experiment split", "total": null, "treatment": 0.4965, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1386165, "name": "Taggstar Message Impressions", "total": 2723498, "treatment": 1337333, "type": "long"}, {"adjusted": null, "control": 0.862, "name": "Taggstar coverage", "total": null, "treatment": 0.8432, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 29641, "control": 15795, "name": "Number of orders", "total": 30572, "treatment": 14777, "type": "long"}, {"adjusted": -1857, "control": null, "name": "Uplift", "total": null, "treatment": -926, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0381, "control": 0.0405, "name": "Conversion rate", "total": 0.0393, "treatment": 0.0381, "type": "percentage"}, {"adjusted": -0.0589, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0589, "type": "percentage"}, {"adjusted": null, "control": 0.0398, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0773, "treatment": 0.0375, "type": "percentage"}, {"adjusted": null, "control": 0.0411, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0798, "treatment": 0.0387, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0878, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": -0.0292, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2553581, "control": 1345033, "name": "Revenue", "total": 2618077, "treatment": 1273044, "type": "currency"}, {"adjusted": null, "control": 85.16, "name": "AOV", "total": 85.64, "treatment": 86.15, "type": "currency"}, {"adjusted": -128615, "control": null, "name": "Uplift", "total": null, "treatment": -64119, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0589, "actualQuantityUplift": -925.59, "adjustedQuantityUplift": -1856.62, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9999999697282997}}]