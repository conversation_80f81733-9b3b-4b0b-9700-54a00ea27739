[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-07-25", "experimentId": "tvc1-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "brooksrunningcomemea", "startDate": "2023-06-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 158344, "name": "Taggstar Experiment Sessions", "total": 317067, "treatment": 158723, "type": "long"}, {"adjusted": null, "control": 0.4994, "name": "Experiment split", "total": null, "treatment": 0.5006, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 323802, "name": "Taggstar Experiment Impressions", "total": 649499, "treatment": 325697, "type": "long"}, {"adjusted": null, "control": 0.4985, "name": "Experiment split", "total": null, "treatment": 0.5015, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 295271, "treatment": 295271, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9066, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 14884, "control": 7186, "name": "Number of orders", "total": 14637, "treatment": 7451, "type": "long"}, {"adjusted": 495, "control": null, "name": "Uplift", "total": null, "treatment": 248, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0469, "control": 0.0454, "name": "Conversion rate", "total": 0.0462, "treatment": 0.0469, "type": "percentage"}, {"adjusted": 0.0344, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0344, "type": "percentage"}, {"adjusted": null, "control": 0.0444, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0903, "treatment": 0.0459, "type": "percentage"}, {"adjusted": null, "control": 0.0464, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0944, "treatment": 0.048, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0109, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0818, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1794287, "control": 860110, "name": "Revenue", "total": 1758326, "treatment": 898216, "type": "currency"}, {"adjusted": null, "control": 119.69, "name": "AOV", "total": 120.13, "treatment": 120.55, "type": "currency"}, {"adjusted": 72008, "control": null, "name": "Uplift", "total": null, "treatment": 36047, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0344, "actualQuantityUplift": 247.8, "adjustedQuantityUplift": 495.01, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.018096902990557655}}]