[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-01-19", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "brooksrunningcomemea", "startDate": "2023-12-06"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 250789, "name": "Taggstar Experiment Sessions", "total": 501802, "treatment": 251013, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 470383, "name": "Taggstar Experiment Impressions", "total": 939506, "treatment": 469123, "type": "long"}, {"adjusted": null, "control": 0.5007, "name": "Experiment split", "total": null, "treatment": 0.4993, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 415930, "name": "Taggstar Message Impressions", "total": 830145, "treatment": 414215, "type": "long"}, {"adjusted": null, "control": 0.8842, "name": "Taggstar coverage", "total": null, "treatment": 0.883, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 15285, "control": 7482, "name": "Number of orders", "total": 15128, "treatment": 7646, "type": "long"}, {"adjusted": 314, "control": null, "name": "Uplift", "total": null, "treatment": 157, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0305, "control": 0.0298, "name": "Conversion rate", "total": 0.0301, "treatment": 0.0305, "type": "percentage"}, {"adjusted": 0.021, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.021, "type": "percentage"}, {"adjusted": null, "control": 0.0292, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.059, "treatment": 0.0298, "type": "percentage"}, {"adjusted": null, "control": 0.0305, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0616, "treatment": 0.0311, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0233, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0674, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1930211, "control": 947004, "name": "Revenue", "total": 1912540, "treatment": 965537, "type": "currency"}, {"adjusted": null, "control": 126.57, "name": "AOV", "total": 126.42, "treatment": 126.28, "type": "currency"}, {"adjusted": 35358, "control": null, "name": "Uplift", "total": null, "treatment": 17687, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.021, "actualQuantityUplift": 157.32, "adjustedQuantityUplift": 314.49, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.0971093936366772}}]