[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-06-15", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "brooksrunningcomemea", "startDate": "2023-05-23"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 85576, "name": "Taggstar Experiment Sessions", "total": 172083, "treatment": 86507, "type": "long"}, {"adjusted": null, "control": 0.4973, "name": "Experiment split", "total": null, "treatment": 0.5027, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 182179, "name": "Taggstar Experiment Impressions", "total": 364403, "treatment": 182224, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 156998, "treatment": 156998, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8616, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 8136, "control": 4177, "name": "Number of orders", "total": 8267, "treatment": 4090, "type": "long"}, {"adjusted": -263, "control": null, "name": "Uplift", "total": null, "treatment": -132, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0473, "control": 0.0488, "name": "Conversion rate", "total": 0.048, "treatment": 0.0473, "type": "percentage"}, {"adjusted": -0.0314, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0314, "type": "percentage"}, {"adjusted": null, "control": 0.0474, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0932, "treatment": 0.0459, "type": "percentage"}, {"adjusted": null, "control": 0.0503, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0989, "treatment": 0.0487, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0873, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.028, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1022376, "control": 536824, "name": "Revenue", "total": 1050778, "treatment": 513954, "type": "currency"}, {"adjusted": null, "control": 128.52, "name": "AOV", "total": 127.11, "treatment": 125.66, "type": "currency"}, {"adjusted": -57113, "control": null, "name": "Uplift", "total": null, "treatment": -28711, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0314, "actualQuantityUplift": -132.44, "adjustedQuantityUplift": -263.46, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.9311988100949817}}]