[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-02-19", "experimentId": "tvc1-v1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "thefragranceshopcouk", "startDate": "2025-01-13"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1048993, "name": "Taggstar Experiment Sessions", "total": 2096328, "treatment": 1047335, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3469639, "name": "Taggstar Experiment Impressions", "total": 6937642, "treatment": 3468003, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 2750539, "treatment": 2750539, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.7931, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 44211, "control": 21561, "name": "Number of orders", "total": 43649, "treatment": 22088, "type": "long"}, {"adjusted": 1123, "control": null, "name": "Uplift", "total": null, "treatment": 561, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0211, "control": 0.0206, "name": "Conversion rate", "total": 0.0208, "treatment": 0.0211, "type": "percentage"}, {"adjusted": 0.0261, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0261, "type": "percentage"}, {"adjusted": null, "control": 0.0203, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0411, "treatment": 0.0208, "type": "percentage"}, {"adjusted": null, "control": 0.0208, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0422, "treatment": 0.0214, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0005, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0534, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3922407, "control": 1901993, "name": "Revenue", "total": 3861646, "treatment": 1959652, "type": "currency"}, {"adjusted": null, "control": 88.21, "name": "AOV", "total": 88.47, "treatment": 88.72, "type": "currency"}, {"adjusted": 121426, "control": null, "name": "Uplift", "total": null, "treatment": 60665, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0261, "actualQuantityUplift": 561.08, "adjustedQuantityUplift": 1123.05, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0033028125762274182}}]