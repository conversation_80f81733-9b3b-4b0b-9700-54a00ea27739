{"averageBasketSize": {"listCount": 1, "seriesList": [{"data": "AverageBasketSize", "dataPoints": [], "metadata": [], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}, "averageOrderValue": {"listCount": 1, "seriesList": [{"data": "AverageOrderValue", "dataPoints": [], "metadata": [], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}, "conversionRates": {"listCount": 1, "seriesList": [{"data": "ConversionRate", "dataPoints": [], "metadata": [], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}, "conversionUplift": {"listCount": 1, "seriesList": [{"data": "ConversionUplift", "dataPoints": [], "metadata": [], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}, "impressionsVolume": {"listCount": 0, "seriesList": [], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": 0, "min": 0, "sum": 0}, "type": "long"}, "metadata": {"categoryEnabled": "true", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2015-09-24", "experimentId": "experiment1", "experimentState": "finished", "interval": "day", "isSeries": "true", "metric": "sessions", "orderType": "conversions", "siteKey": "argoscouk", "startDate": "2015-06-12"}, "msgImpressionsSeries": {"listCount": 0, "seriesList": [], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": 0, "min": 0, "sum": 0}, "type": "long"}, "orderUplift": {"listCount": 1, "seriesList": [{"data": "OrdersUplift", "dataPoints": [], "metadata": [], "stats": {"average": 0.0, "count": 0, "max": 0, "min": 0, "sum": 0}, "type": "long"}], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": 0, "min": 0, "sum": 0}, "type": "long"}, "orderVolume": {"listCount": 0, "seriesList": [], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": 0, "min": 0, "sum": 0}, "type": "long"}, "pageDepthSeries": {"listCount": 1, "seriesList": [{"data": "<PERSON><PERSON><PERSON><PERSON>", "dataPoints": [], "metadata": [], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}, "revenueUplift": {"listCount": 1, "seriesList": [{"data": "RevenueUplift", "dataPoints": [], "metadata": [], "stats": {"average": 0.0, "count": 0, "max": 0, "min": 0, "sum": 0}, "type": "decimal"}], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": 0, "min": 0, "sum": 0}, "type": "decimal"}, "sessionsVolume": {"listCount": 0, "seriesList": [], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": 0, "min": 0, "sum": 0}, "type": "long"}, "siteKey": "argoscouk", "statSig": {"listCount": 0, "seriesList": [], "siteKeys": ["argoscouk"], "stats": null, "type": "boolean"}, "statSigValueSeries": {"listCount": 1, "seriesList": [{"data": "StatisticalSignificanceValue", "dataPoints": [], "metadata": [], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}], "siteKeys": ["argoscouk"], "stats": {"average": 0.0, "count": 0, "max": "-Infinity", "min": "Infinity", "sum": 0.0}, "type": "decimal"}}