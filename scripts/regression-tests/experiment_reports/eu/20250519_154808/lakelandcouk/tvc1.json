[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-06-12", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lakelandcouk", "startDate": "2023-05-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 748073, "name": "Taggstar Experiment Sessions", "total": 1496593, "treatment": 748520, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2169584, "name": "Taggstar Experiment Impressions", "total": 4345757, "treatment": 2176173, "type": "long"}, {"adjusted": null, "control": 0.4992, "name": "Experiment split", "total": null, "treatment": 0.5008, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 1692025, "treatment": 1692025, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.7775, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 45802, "control": 21815, "name": "Number of orders", "total": 44723, "treatment": 22908, "type": "long"}, {"adjusted": 2159, "control": null, "name": "Uplift", "total": null, "treatment": 1080, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0306, "control": 0.0292, "name": "Conversion rate", "total": 0.0299, "treatment": 0.0306, "type": "percentage"}, {"adjusted": 0.0495, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0495, "type": "percentage"}, {"adjusted": null, "control": 0.0288, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.059, "treatment": 0.0302, "type": "percentage"}, {"adjusted": null, "control": 0.0295, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0605, "treatment": 0.031, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0227, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0769, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2905157, "control": 1379600, "name": "Revenue", "total": 2832613, "treatment": 1453012, "type": "currency"}, {"adjusted": null, "control": 63.24, "name": "AOV", "total": 63.34, "treatment": 63.43, "type": "currency"}, {"adjusted": 145132, "control": null, "name": "Uplift", "total": null, "treatment": 72588, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0495, "actualQuantityUplift": 1079.96, "adjustedQuantityUplift": 2159.28, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.0897615364656044e-07}}]