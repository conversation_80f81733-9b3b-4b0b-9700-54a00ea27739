[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-10-14", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "theperfumeshopcom", "startDate": "2022-07-13"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2589588, "name": "Taggstar Experiment Sessions", "total": 5186240, "treatment": 2596652, "type": "long"}, {"adjusted": null, "control": 0.4993, "name": "Experiment split", "total": null, "treatment": 0.5007, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 8117834, "name": "Taggstar Experiment Impressions", "total": 16248102, "treatment": 8130268, "type": "long"}, {"adjusted": null, "control": 0.4996, "name": "Experiment split", "total": null, "treatment": 0.5004, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 7733455, "name": "Taggstar Message Impressions", "total": 15477308, "treatment": 7743853, "type": "long"}, {"adjusted": null, "control": 0.9527, "name": "Taggstar coverage", "total": null, "treatment": 0.9525, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 195094, "control": 97408, "name": "Number of orders", "total": 195088, "treatment": 97680, "type": "long"}, {"adjusted": 13, "control": null, "name": "Uplift", "total": null, "treatment": 6, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0376, "control": 0.0376, "name": "Conversion rate", "total": 0.0376, "treatment": 0.0376, "type": "percentage"}, {"adjusted": 0.0001, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0001, "type": "percentage"}, {"adjusted": null, "control": 0.0374, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0748, "treatment": 0.0374, "type": "percentage"}, {"adjusted": null, "control": 0.0378, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0757, "treatment": 0.0378, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0122, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0125, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 13116426, "control": 6524509, "name": "Revenue", "total": 13091655, "treatment": 6567145, "type": "currency"}, {"adjusted": null, "control": 66.98, "name": "AOV", "total": 67.11, "treatment": 67.23, "type": "currency"}, {"adjusted": 49609, "control": null, "name": "Uplift", "total": null, "treatment": 24838, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0001, "actualQuantityUplift": 6.29, "adjustedQuantityUplift": 12.55, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.49422064119097703}}]