[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-11-29", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "lifestylesportscomie", "startDate": "2023-11-24"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 153241, "name": "Taggstar Experiment Sessions", "total": 306034, "treatment": 152793, "type": "long"}, {"adjusted": null, "control": 0.5007, "name": "Experiment split", "total": null, "treatment": 0.4993, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 686192, "name": "Taggstar Experiment Impressions", "total": 1374798, "treatment": 688606, "type": "long"}, {"adjusted": null, "control": 0.4991, "name": "Experiment split", "total": null, "treatment": 0.5009, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 626321, "name": "Taggstar Message Impressions", "total": 1253565, "treatment": 627244, "type": "long"}, {"adjusted": null, "control": 0.9127, "name": "Taggstar coverage", "total": null, "treatment": 0.9109, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 17221, "control": 8421, "name": "Number of orders", "total": 17019, "treatment": 8598, "type": "long"}, {"adjusted": 404, "control": null, "name": "Uplift", "total": null, "treatment": 202, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0563, "control": 0.055, "name": "Conversion rate", "total": 0.0556, "treatment": 0.0563, "type": "percentage"}, {"adjusted": 0.024, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.024, "type": "percentage"}, {"adjusted": null, "control": 0.0538, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1089, "treatment": 0.0551, "type": "percentage"}, {"adjusted": null, "control": 0.0561, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1135, "treatment": 0.0574, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0174, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0672, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1546357, "control": 755260, "name": "Revenue", "total": 1527307, "treatment": 772047, "type": "currency"}, {"adjusted": null, "control": 89.69, "name": "AOV", "total": 89.74, "treatment": 89.79, "type": "currency"}, {"adjusted": 38046, "control": null, "name": "Uplift", "total": null, "treatment": 18995, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.024, "actualQuantityUplift": 201.62, "adjustedQuantityUplift": 403.83, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.05561988047360944}}]