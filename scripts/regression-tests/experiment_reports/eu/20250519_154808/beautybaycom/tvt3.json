[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-08-28", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "beautybaycom", "startDate": "2024-07-05"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 810942, "name": "Taggstar Experiment Sessions", "total": 1623762, "treatment": 812820, "type": "long"}, {"adjusted": null, "control": 0.4994, "name": "Experiment split", "total": null, "treatment": 0.5006, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4147722, "name": "Taggstar Experiment Impressions", "total": 8326723, "treatment": 4179001, "type": "long"}, {"adjusted": null, "control": 0.4981, "name": "Experiment split", "total": null, "treatment": 0.5019, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 3105858, "name": "Taggstar Message Impressions", "total": 6233898, "treatment": 3128040, "type": "long"}, {"adjusted": null, "control": 0.7488, "name": "Taggstar coverage", "total": null, "treatment": 0.7485, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 79146, "control": 38392, "name": "Number of orders", "total": 78011, "treatment": 39619, "type": "long"}, {"adjusted": 2274, "control": null, "name": "Uplift", "total": null, "treatment": 1138, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0487, "control": 0.0473, "name": "Conversion rate", "total": 0.048, "treatment": 0.0487, "type": "percentage"}, {"adjusted": 0.0296, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0296, "type": "percentage"}, {"adjusted": null, "control": 0.0469, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0952, "treatment": 0.0483, "type": "percentage"}, {"adjusted": null, "control": 0.0478, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.097, "treatment": 0.0492, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0098, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0497, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3442701, "control": 1703776, "name": "Revenue", "total": 3427117, "treatment": 1723341, "type": "currency"}, {"adjusted": null, "control": 44.38, "name": "AOV", "total": 43.93, "treatment": 43.5, "type": "currency"}, {"adjusted": 31204, "control": null, "name": "Uplift", "total": null, "treatment": 15620, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0296, "actualQuantityUplift": 1138.09, "adjustedQuantityUplift": 2273.55, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.5126762930657371e-05}}]