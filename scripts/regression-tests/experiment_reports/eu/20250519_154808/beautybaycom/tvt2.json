[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-06-24", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "beautybaycom", "startDate": "2024-05-03"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 916760, "name": "Taggstar Experiment Sessions", "total": 1828124, "treatment": 911364, "type": "long"}, {"adjusted": null, "control": 0.5015, "name": "Experiment split", "total": null, "treatment": 0.4985, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5021063, "name": "Taggstar Experiment Impressions", "total": 10045179, "treatment": 5024116, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 3799496, "name": "Taggstar Message Impressions", "total": 7603916, "treatment": 3804420, "type": "long"}, {"adjusted": null, "control": 0.7567, "name": "Taggstar coverage", "total": null, "treatment": 0.7572, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 89434, "control": 41319, "name": "Number of orders", "total": 85904, "treatment": 44585, "type": "long"}, {"adjusted": 7039, "control": null, "name": "Uplift", "total": null, "treatment": 3509, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0489, "control": 0.0451, "name": "Conversion rate", "total": 0.047, "treatment": 0.0489, "type": "percentage"}, {"adjusted": 0.0854, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0854, "type": "percentage"}, {"adjusted": null, "control": 0.0446, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0931, "treatment": 0.0485, "type": "percentage"}, {"adjusted": null, "control": 0.0455, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0949, "treatment": 0.0494, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0656, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.1057, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3786813, "control": 1827288, "name": "Revenue", "total": 3715105, "treatment": 1887818, "type": "currency"}, {"adjusted": null, "control": 44.22, "name": "AOV", "total": 43.25, "treatment": 42.34, "type": "currency"}, {"adjusted": 142992, "control": null, "name": "Uplift", "total": null, "treatment": 71285, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0854, "actualQuantityUplift": 3509.2, "adjustedQuantityUplift": 7039.18, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 4.532271281718526e-35}}]