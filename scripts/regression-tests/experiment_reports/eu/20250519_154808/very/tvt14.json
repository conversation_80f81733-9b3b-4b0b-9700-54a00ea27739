[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-08-04", "experimentId": "tvt14", "experimentState": "finished", "isSeries": "false", "metric": "impressions", "orderType": "conversions", "siteKey": "very", "startDate": "2023-07-25"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Experiment Sessions", "total": 0, "treatment": 0, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Experiment split", "total": null, "treatment": 0.0, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 25388054, "name": "Taggstar Experiment Impressions", "total": 50900405, "treatment": 25512351, "type": "long"}, {"adjusted": null, "control": 0.4988, "name": "Experiment split", "total": null, "treatment": 0.5012, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 20797675, "name": "Taggstar Message Impressions", "total": 39976923, "treatment": 19179248, "type": "long"}, {"adjusted": null, "control": 0.8192, "name": "Taggstar coverage", "total": null, "treatment": 0.7518, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 298882, "control": 150882, "name": "Number of orders", "total": 300688, "treatment": 149806, "type": "long"}, {"adjusted": -3621, "control": null, "name": "Uplift", "total": null, "treatment": -1815, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0059, "control": 0.0059, "name": "Conversion rate", "total": 0.0059, "treatment": 0.0059, "type": "percentage"}, {"adjusted": -0.012, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.012, "type": "percentage"}, {"adjusted": null, "control": 0.0059, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0118, "treatment": 0.0058, "type": "percentage"}, {"adjusted": null, "control": 0.006, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0119, "treatment": 0.0059, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0219, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": -0.002, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 48407664, "control": 24242102, "name": "Revenue", "total": 48505038, "treatment": 24262937, "type": "currency"}, {"adjusted": null, "control": 160.67, "name": "AOV", "total": 161.31, "treatment": 161.96, "type": "currency"}, {"adjusted": -195226, "control": null, "name": "Uplift", "total": null, "treatment": -97851, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.012, "actualQuantityUplift": -1814.7, "adjustedQuantityUplift": -3620.56, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9995352799627902}}]