[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-05-17", "experimentId": "tvt5", "experimentState": "finished", "isSeries": "false", "metric": "impressions", "orderType": "conversions", "siteKey": "very", "startDate": "2022-05-06"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Experiment Sessions", "total": 0, "treatment": 0, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Experiment split", "total": null, "treatment": 0.0, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 36472442, "name": "Taggstar Experiment Impressions", "total": 72814665, "treatment": 36342223, "type": "long"}, {"adjusted": null, "control": 0.5009, "name": "Experiment split", "total": null, "treatment": 0.4991, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 30172115, "name": "Taggstar Message Impressions", "total": 60490359, "treatment": 30318244, "type": "long"}, {"adjusted": null, "control": 0.8273, "name": "Taggstar coverage", "total": null, "treatment": 0.8342, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 384800, "control": 193223, "name": "Number of orders", "total": 385279, "treatment": 192056, "type": "long"}, {"adjusted": -956, "control": null, "name": "Uplift", "total": null, "treatment": -477, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0053, "control": 0.0053, "name": "Conversion rate", "total": 0.0053, "treatment": 0.0053, "type": "percentage"}, {"adjusted": -0.0025, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0025, "type": "percentage"}, {"adjusted": null, "control": 0.0053, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0105, "treatment": 0.0053, "type": "percentage"}, {"adjusted": null, "control": 0.0053, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0106, "treatment": 0.0053, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0113, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0064, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 53833021, "control": 27009061, "name": "Revenue", "total": 53877435, "treatment": 26868374, "type": "currency"}, {"adjusted": null, "control": 139.78, "name": "AOV", "total": 139.84, "treatment": 139.9, "type": "currency"}, {"adjusted": -88669, "control": null, "name": "Uplift", "total": null, "treatment": -44255, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0025, "actualQuantityUplift": -477.13, "adjustedQuantityUplift": -955.97, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7799743001129651}}]