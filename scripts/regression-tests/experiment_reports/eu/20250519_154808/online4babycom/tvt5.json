[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-12-19", "experimentId": "tvt5", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "online4babycom", "startDate": "2024-11-18"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 393457, "name": "Taggstar Experiment Sessions", "total": 786263, "treatment": 392806, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 984298, "name": "Taggstar Experiment Impressions", "total": 1966875, "treatment": 982577, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 851224, "name": "Taggstar Message Impressions", "total": 1703874, "treatment": 852650, "type": "long"}, {"adjusted": null, "control": 0.8648, "name": "Taggstar coverage", "total": null, "treatment": 0.8678, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 25171, "control": 12768, "name": "Number of orders", "total": 25343, "treatment": 12575, "type": "long"}, {"adjusted": -344, "control": null, "name": "Uplift", "total": null, "treatment": -172, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.032, "control": 0.0325, "name": "Conversion rate", "total": 0.0322, "treatment": 0.032, "type": "percentage"}, {"adjusted": -0.0135, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0135, "type": "percentage"}, {"adjusted": null, "control": 0.0319, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0634, "treatment": 0.0315, "type": "percentage"}, {"adjusted": null, "control": 0.033, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0656, "treatment": 0.0326, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0467, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0209, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2400881, "control": 1216811, "name": "Revenue", "total": 2416257, "treatment": 1199447, "type": "currency"}, {"adjusted": null, "control": 95.3, "name": "AOV", "total": 95.34, "treatment": 95.38, "type": "currency"}, {"adjusted": -30727, "control": null, "name": "Uplift", "total": null, "treatment": -15351, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0135, "actualQuantityUplift": -171.87, "adjustedQuantityUplift": -344.03, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.863984327201331}}]