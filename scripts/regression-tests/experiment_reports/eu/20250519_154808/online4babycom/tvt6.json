[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-05-13", "experimentId": "tvt6", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "online4babycom", "startDate": "2025-01-13"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1726929, "name": "Taggstar Experiment Sessions", "total": 3460281, "treatment": 1733352, "type": "long"}, {"adjusted": null, "control": 0.4991, "name": "Experiment split", "total": null, "treatment": 0.5009, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4465278, "name": "Taggstar Experiment Impressions", "total": 8939109, "treatment": 4473831, "type": "long"}, {"adjusted": null, "control": 0.4995, "name": "Experiment split", "total": null, "treatment": 0.5005, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 3995841, "name": "Taggstar Message Impressions", "total": 8011022, "treatment": 4015181, "type": "long"}, {"adjusted": null, "control": 0.8949, "name": "Taggstar coverage", "total": null, "treatment": 0.8975, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 98176, "control": 49298, "name": "Number of orders", "total": 98477, "treatment": 49179, "type": "long"}, {"adjusted": -604, "control": null, "name": "Uplift", "total": null, "treatment": -302, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0284, "control": 0.0285, "name": "Conversion rate", "total": 0.0285, "treatment": 0.0284, "type": "percentage"}, {"adjusted": -0.0061, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0061, "type": "percentage"}, {"adjusted": null, "control": 0.0283, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0564, "treatment": 0.0281, "type": "percentage"}, {"adjusted": null, "control": 0.0288, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0574, "treatment": 0.0286, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0233, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0113, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 10043810, "control": 5119437, "name": "Revenue", "total": 10150663, "treatment": 5031227, "type": "currency"}, {"adjusted": null, "control": 103.85, "name": "AOV", "total": 103.08, "treatment": 102.3, "type": "currency"}, {"adjusted": -214104, "control": null, "name": "Uplift", "total": null, "treatment": -107251, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0061, "actualQuantityUplift": -302.35, "adjustedQuantityUplift": -603.59, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8353903298960161}}]