[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-12-28", "experimentId": "tvt4", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-12-07"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 609360, "name": "Taggstar Experiment Sessions", "total": 1219301, "treatment": 609941, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4685542, "name": "Taggstar Experiment Impressions", "total": 9358782, "treatment": 4673240, "type": "long"}, {"adjusted": null, "control": 0.5007, "name": "Experiment split", "total": null, "treatment": 0.4993, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 3732650, "name": "Taggstar Message Impressions", "total": 7449293, "treatment": 3716643, "type": "long"}, {"adjusted": null, "control": 0.7966, "name": "Taggstar coverage", "total": null, "treatment": 0.7953, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 85571, "control": 43308, "name": "Number of orders", "total": 86114, "treatment": 42806, "type": "long"}, {"adjusted": -1086, "control": null, "name": "Uplift", "total": null, "treatment": -543, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0702, "control": 0.0711, "name": "Conversion rate", "total": 0.0706, "treatment": 0.0702, "type": "percentage"}, {"adjusted": -0.0125, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0125, "type": "percentage"}, {"adjusted": null, "control": 0.0704, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.14, "treatment": 0.0695, "type": "percentage"}, {"adjusted": null, "control": 0.0717, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1425, "treatment": 0.0708, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0304, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0056, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 7330218, "control": 3722446, "name": "Revenue", "total": 7389301, "treatment": 3666856, "type": "currency"}, {"adjusted": null, "control": 85.95, "name": "AOV", "total": 85.81, "treatment": 85.66, "type": "currency"}, {"adjusted": -118223, "control": null, "name": "Uplift", "total": null, "treatment": -59140, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0125, "actualQuantityUplift": -543.29, "adjustedQuantityUplift": -1086.07, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.972541085151165}}]