[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2021-06-17", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2021-04-13"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3454715, "name": "Taggstar Experiment Sessions", "total": 6901852, "treatment": 3447137, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 8526395, "name": "Taggstar Experiment Impressions", "total": 16985492, "treatment": 8459097, "type": "long"}, {"adjusted": null, "control": 0.502, "name": "Experiment split", "total": null, "treatment": 0.498, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 5181880, "name": "Taggstar Message Impressions", "total": 8369306, "treatment": 3187426, "type": "long"}, {"adjusted": null, "control": 0.6077, "name": "Taggstar coverage", "total": null, "treatment": 0.3768, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 130471, "control": 65471, "name": "Number of orders", "total": 130635, "treatment": 65164, "type": "long"}, {"adjusted": -327, "control": null, "name": "Uplift", "total": null, "treatment": -163, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0189, "control": 0.019, "name": "Conversion rate", "total": 0.0189, "treatment": 0.0189, "type": "percentage"}, {"adjusted": -0.0025, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0025, "type": "percentage"}, {"adjusted": null, "control": 0.0188, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0376, "treatment": 0.0188, "type": "percentage"}, {"adjusted": null, "control": 0.0191, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0381, "treatment": 0.019, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0175, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0128, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 16622859, "control": 8371396, "name": "Revenue", "total": 16673700, "treatment": 8302304, "type": "currency"}, {"adjusted": null, "control": 127.86, "name": "AOV", "total": 127.64, "treatment": 127.41, "type": "currency"}, {"adjusted": -101570, "control": null, "name": "Uplift", "total": null, "treatment": -50729, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0025, "actualQuantityUplift": -163.39, "adjustedQuantityUplift": -327.13, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.6761268620777496}}]