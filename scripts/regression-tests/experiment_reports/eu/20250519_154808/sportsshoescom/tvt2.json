[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-02-21", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "sportsshoescom", "startDate": "2021-11-18"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6164808, "name": "Taggstar Experiment Sessions", "total": 12340132, "treatment": 6175324, "type": "long"}, {"adjusted": null, "control": 0.4996, "name": "Experiment split", "total": null, "treatment": 0.5004, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 25068797, "name": "Taggstar Experiment Impressions", "total": 50154483, "treatment": 25085686, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 14819722, "name": "Taggstar Message Impressions", "total": 29648553, "treatment": 14828831, "type": "long"}, {"adjusted": null, "control": 0.5912, "name": "Taggstar coverage", "total": null, "treatment": 0.5911, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 327635, "control": 162905, "name": "Number of orders", "total": 326862, "treatment": 163957, "type": "long"}, {"adjusted": 1547, "control": null, "name": "Uplift", "total": null, "treatment": 774, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0266, "control": 0.0264, "name": "Conversion rate", "total": 0.0265, "treatment": 0.0266, "type": "percentage"}, {"adjusted": 0.0047, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0047, "type": "percentage"}, {"adjusted": null, "control": 0.0263, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0527, "treatment": 0.0264, "type": "percentage"}, {"adjusted": null, "control": 0.0266, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0532, "treatment": 0.0267, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0048, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0144, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 0, "control": 0, "name": "Revenue", "total": 0, "treatment": 0, "type": "currency"}, {"adjusted": null, "control": 0.0, "name": "AOV", "total": 0.0, "treatment": 0.0, "type": "currency"}, {"adjusted": 0, "control": null, "name": "Uplift", "total": null, "treatment": 0, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0047, "actualQuantityUplift": 774.11, "adjustedQuantityUplift": 1546.91, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.08516496111544741}}]