[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-11-04", "experimentId": "tvc5", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "sportsshoescom", "startDate": "2024-10-01"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1364773, "name": "Taggstar Experiment Sessions", "total": 2735462, "treatment": 1370689, "type": "long"}, {"adjusted": null, "control": 0.4989, "name": "Experiment split", "total": null, "treatment": 0.5011, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5944209, "name": "Taggstar Experiment Impressions", "total": 11900068, "treatment": 5955859, "type": "long"}, {"adjusted": null, "control": 0.4995, "name": "Experiment split", "total": null, "treatment": 0.5005, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 4793522, "treatment": 4793522, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8048, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 66680, "control": 32690, "name": "Number of orders", "total": 66102, "treatment": 33412, "type": "long"}, {"adjusted": 1158, "control": null, "name": "Uplift", "total": null, "treatment": 580, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0244, "control": 0.024, "name": "Conversion rate", "total": 0.0242, "treatment": 0.0244, "type": "percentage"}, {"adjusted": 0.0177, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0177, "type": "percentage"}, {"adjusted": null, "control": 0.0237, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0478, "treatment": 0.0241, "type": "percentage"}, {"adjusted": null, "control": 0.0242, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0488, "treatment": 0.0246, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0038, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0396, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 6617929, "control": 3257088, "name": "Revenue", "total": 6573209, "treatment": 3316121, "type": "currency"}, {"adjusted": null, "control": 99.64, "name": "AOV", "total": 99.44, "treatment": 99.25, "type": "currency"}, {"adjusted": 89634, "control": null, "name": "Uplift", "total": null, "treatment": 44914, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0177, "actualQuantityUplift": 580.3, "adjustedQuantityUplift": 1158.09, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.011305673994007406}}]