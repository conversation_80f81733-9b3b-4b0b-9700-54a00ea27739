[{"category": "Overall", "metaData": {"categoryEnabled": "true", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2019-09-29", "experimentId": "experiment11", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "argoscouk", "startDate": "2019-09-11"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 12194987, "name": "Taggstar Experiment Sessions", "total": 24390744, "treatment": 12195757, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 26910106, "name": "Taggstar Experiment Impressions", "total": 53795961, "treatment": 26885855, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 26039745, "name": "Taggstar Message Impressions", "total": 52065121, "treatment": 26025376, "type": "long"}, {"adjusted": null, "control": 0.9677, "name": "Taggstar coverage", "total": null, "treatment": 0.968, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 1432455, "control": 719391, "name": "Number of orders", "total": 1435641, "treatment": 716250, "type": "long"}, {"adjusted": -6373, "control": null, "name": "Uplift", "total": null, "treatment": -3186, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0587, "control": 0.059, "name": "Conversion rate", "total": 0.0589, "treatment": 0.0587, "type": "percentage"}, {"adjusted": -0.0044, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0044, "type": "percentage"}, {"adjusted": null, "control": 0.0589, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1175, "treatment": 0.0586, "type": "percentage"}, {"adjusted": null, "control": 0.0591, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.118, "treatment": 0.0589, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0089, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 106764716, "control": 53596622, "name": "Revenue", "total": 106980665, "treatment": 53384043, "type": "currency"}, {"adjusted": null, "control": 74.5, "name": "AOV", "total": 74.52, "treatment": 74.53, "type": "currency"}, {"adjusted": -431912, "control": null, "name": "Uplift", "total": null, "treatment": -215963, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0044, "actualQuantityUplift": -3186.42, "adjustedQuantityUplift": -6372.64, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9969391787819922}}]