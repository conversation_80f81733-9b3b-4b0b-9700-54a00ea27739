[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2019-10-21", "experimentId": "experiment2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "simplybecouk", "startDate": "2017-03-01"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3792446, "name": "Taggstar Experiment Sessions", "total": 75055046, "treatment": 71262600, "type": "long"}, {"adjusted": null, "control": 0.0505, "name": "Experiment split", "total": null, "treatment": 0.9495, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 13539295, "name": "Taggstar Experiment Impressions", "total": 270628649, "treatment": 257089354, "type": "long"}, {"adjusted": null, "control": 0.05, "name": "Experiment split", "total": null, "treatment": 0.95, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 180282276, "treatment": 180282276, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.7012, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 5160409, "control": 253009, "name": "Number of orders", "total": 5152668, "treatment": 4899659, "type": "long"}, {"adjusted": 153191, "control": null, "name": "Uplift", "total": null, "treatment": 145450, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0688, "control": 0.0667, "name": "Conversion rate", "total": 0.0677, "treatment": 0.0688, "type": "percentage"}, {"adjusted": 0.0306, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0306, "type": "percentage"}, {"adjusted": null, "control": 0.0665, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1352, "treatment": 0.0687, "type": "percentage"}, {"adjusted": null, "control": 0.067, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1358, "treatment": 0.0688, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0259, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0354, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 401804074, "control": 19671597, "name": "Revenue", "total": 401172969, "treatment": 381501372, "type": "currency"}, {"adjusted": null, "control": 77.75, "name": "AOV", "total": 77.86, "treatment": 77.86, "type": "currency"}, {"adjusted": 12489995, "control": null, "name": "Uplift", "total": null, "treatment": 11858890, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0306, "actualQuantityUplift": 145450.46, "adjustedQuantityUplift": 153191.03, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.4713917038096498e-54}}]