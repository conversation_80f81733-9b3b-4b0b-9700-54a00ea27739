[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-08-04", "experimentId": "TvC3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "gymkingcom", "startDate": "2023-04-11"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 863961, "name": "Taggstar Experiment Sessions", "total": 1727335, "treatment": 863374, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4694022, "name": "Taggstar Experiment Impressions", "total": 9379167, "treatment": 4685145, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 3262170, "treatment": 3262170, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.6963, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 34462, "control": 16726, "name": "Number of orders", "total": 33951, "treatment": 17225, "type": "long"}, {"adjusted": 1021, "control": null, "name": "Uplift", "total": null, "treatment": 510, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.02, "control": 0.0194, "name": "Conversion rate", "total": 0.0197, "treatment": 0.02, "type": "percentage"}, {"adjusted": 0.0305, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0305, "type": "percentage"}, {"adjusted": null, "control": 0.0191, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0387, "treatment": 0.0197, "type": "percentage"}, {"adjusted": null, "control": 0.0197, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0399, "treatment": 0.0202, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0003, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0617, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 28690197, "control": 5088667, "name": "Revenue", "total": 19428891, "treatment": 14340224, "type": "currency"}, {"adjusted": null, "control": 304.24, "name": "AOV", "total": 572.26, "treatment": 832.52, "type": "currency"}, {"adjusted": 18516320, "control": null, "name": "Uplift", "total": null, "treatment": 9255014, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0305, "actualQuantityUplift": 510.36, "adjustedQuantityUplift": 1021.08, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0025677712507878696}}]