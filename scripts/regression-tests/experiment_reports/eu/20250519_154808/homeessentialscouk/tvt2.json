[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-04-11", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "homeessentialscouk", "startDate": "2023-03-31"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 24100, "name": "Taggstar Experiment Sessions", "total": 48494, "treatment": 24394, "type": "long"}, {"adjusted": null, "control": 0.497, "name": "Experiment split", "total": null, "treatment": 0.503, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 205815, "name": "Taggstar Experiment Impressions", "total": 418166, "treatment": 212351, "type": "long"}, {"adjusted": null, "control": 0.4922, "name": "Experiment split", "total": null, "treatment": 0.5078, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 66099, "name": "Taggstar Message Impressions", "total": 132814, "treatment": 66715, "type": "long"}, {"adjusted": null, "control": 0.3212, "name": "Taggstar coverage", "total": null, "treatment": 0.3142, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 4395, "control": 2221, "name": "Number of orders", "total": 4432, "treatment": 2211, "type": "long"}, {"adjusted": -74, "control": null, "name": "Uplift", "total": null, "treatment": -37, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0906, "control": 0.0922, "name": "Conversion rate", "total": 0.0914, "treatment": 0.0906, "type": "percentage"}, {"adjusted": -0.0165, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0165, "type": "percentage"}, {"adjusted": null, "control": 0.0885, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1755, "treatment": 0.087, "type": "percentage"}, {"adjusted": null, "control": 0.0958, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.19, "treatment": 0.0942, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0916, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0648, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 574895, "control": 292827, "name": "Revenue", "total": 582017, "treatment": 289190, "type": "currency"}, {"adjusted": null, "control": 131.84, "name": "AOV", "total": 131.32, "treatment": 130.8, "type": "currency"}, {"adjusted": -14331, "control": null, "name": "Uplift", "total": null, "treatment": -7209, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0165, "actualQuantityUplift": -37.09, "adjustedQuantityUplift": -73.74, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7193764198374231}}]