[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-08-26", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "ctshirtscom", "startDate": "2022-07-19"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1499624, "name": "Taggstar Experiment Sessions", "total": 3003442, "treatment": 1503818, "type": "long"}, {"adjusted": null, "control": 0.4993, "name": "Experiment split", "total": null, "treatment": 0.5007, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 10653038, "name": "Taggstar Experiment Impressions", "total": 21409719, "treatment": 10756681, "type": "long"}, {"adjusted": null, "control": 0.4976, "name": "Experiment split", "total": null, "treatment": 0.5024, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 10118312, "name": "Taggstar Message Impressions", "total": 15361651, "treatment": 5243339, "type": "long"}, {"adjusted": null, "control": 0.9498, "name": "Taggstar coverage", "total": null, "treatment": 0.4874, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 129907, "control": 65373, "name": "Number of orders", "total": 130417, "treatment": 65044, "type": "long"}, {"adjusted": -1022, "control": null, "name": "Uplift", "total": null, "treatment": -512, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0433, "control": 0.0436, "name": "Conversion rate", "total": 0.0434, "treatment": 0.0433, "type": "percentage"}, {"adjusted": -0.0078, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0078, "type": "percentage"}, {"adjusted": null, "control": 0.0433, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0862, "treatment": 0.0429, "type": "percentage"}, {"adjusted": null, "control": 0.0439, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0875, "treatment": 0.0436, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0226, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0072, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 19934300, "control": 10046629, "name": "Revenue", "total": 20027697, "treatment": 9981068, "type": "currency"}, {"adjusted": null, "control": 153.68, "name": "AOV", "total": 153.57, "treatment": 153.45, "type": "currency"}, {"adjusted": -187054, "control": null, "name": "Uplift", "total": null, "treatment": -93658, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0078, "actualQuantityUplift": -511.83, "adjustedQuantityUplift": -1022.23, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.9260610509578238}}]