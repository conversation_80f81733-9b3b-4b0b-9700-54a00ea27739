[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-10-25", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-09-01"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3068312, "name": "Taggstar Experiment Sessions", "total": 6137337, "treatment": 3069025, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 18998553, "name": "Taggstar Experiment Impressions", "total": 38047159, "treatment": 19048606, "type": "long"}, {"adjusted": null, "control": 0.4993, "name": "Experiment split", "total": null, "treatment": 0.5007, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 13557386, "name": "Taggstar Message Impressions", "total": 27146107, "treatment": 13588721, "type": "long"}, {"adjusted": null, "control": 0.7136, "name": "Taggstar coverage", "total": null, "treatment": 0.7134, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 26719, "control": 13515, "name": "Number of orders", "total": 26876, "treatment": 13361, "type": "long"}, {"adjusted": -314, "control": null, "name": "Uplift", "total": null, "treatment": -157, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0044, "control": 0.0044, "name": "Conversion rate", "total": 0.0044, "treatment": 0.0044, "type": "percentage"}, {"adjusted": -0.0116, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0116, "type": "percentage"}, {"adjusted": null, "control": 0.0043, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0086, "treatment": 0.0043, "type": "percentage"}, {"adjusted": null, "control": 0.0045, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0089, "treatment": 0.0044, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0444, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0223, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 13726761, "control": 7154302, "name": "Revenue", "total": 14018480, "treatment": 6864178, "type": "currency"}, {"adjusted": null, "control": 529.36, "name": "AOV", "total": 521.6, "treatment": 513.75, "type": "currency"}, {"adjusted": -583505, "control": null, "name": "Uplift", "total": null, "treatment": -291787, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0116, "actualQuantityUplift": -157.14, "adjustedQuantityUplift": -314.24, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8316042328192461}}]