[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-07-18", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "online4babycom", "startDate": "2023-06-10"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 534573, "name": "Taggstar Experiment Sessions", "total": 1070901, "treatment": 536328, "type": "long"}, {"adjusted": null, "control": 0.4992, "name": "Experiment split", "total": null, "treatment": 0.5008, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1410433, "name": "Taggstar Experiment Impressions", "total": 2840393, "treatment": 1429960, "type": "long"}, {"adjusted": null, "control": 0.4966, "name": "Experiment split", "total": null, "treatment": 0.5034, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 971141, "name": "Taggstar Message Impressions", "total": 2214179, "treatment": 1243038, "type": "long"}, {"adjusted": null, "control": 0.6885, "name": "Taggstar coverage", "total": null, "treatment": 0.8693, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 23845, "control": 11741, "name": "Number of orders", "total": 23683, "treatment": 11942, "type": "long"}, {"adjusted": 324, "control": null, "name": "Uplift", "total": null, "treatment": 162, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0223, "control": 0.022, "name": "Conversion rate", "total": 0.0221, "treatment": 0.0223, "type": "percentage"}, {"adjusted": 0.0138, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0138, "type": "percentage"}, {"adjusted": null, "control": 0.0216, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0434, "treatment": 0.0219, "type": "percentage"}, {"adjusted": null, "control": 0.0224, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.045, "treatment": 0.0227, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0217, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0506, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3003669, "control": 1475932, "name": "Revenue", "total": 2980227, "treatment": 1504296, "type": "currency"}, {"adjusted": null, "control": 125.71, "name": "AOV", "total": 125.84, "treatment": 125.97, "type": "currency"}, {"adjusted": 46961, "control": null, "name": "Uplift", "total": null, "treatment": 23519, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0138, "actualQuantityUplift": 162.45, "adjustedQuantityUplift": 324.38, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.1432644110678163}}]