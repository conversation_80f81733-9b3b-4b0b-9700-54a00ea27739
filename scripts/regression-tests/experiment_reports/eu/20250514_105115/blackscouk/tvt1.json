[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-09-22", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "impressions", "orderType": "conversions", "siteKey": "blackscouk", "startDate": "2023-09-01"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 392088, "name": "Taggstar Experiment Sessions", "total": 784659, "treatment": 392571, "type": "long"}, {"adjusted": null, "control": 0.4997, "name": "Experiment split", "total": null, "treatment": 0.5003, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1484687, "name": "Taggstar Experiment Impressions", "total": 2978346, "treatment": 1493659, "type": "long"}, {"adjusted": null, "control": 0.4985, "name": "Experiment split", "total": null, "treatment": 0.5015, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1360188, "name": "Taggstar Message Impressions", "total": 2728059, "treatment": 1367871, "type": "long"}, {"adjusted": null, "control": 0.9161, "name": "Taggstar coverage", "total": null, "treatment": 0.9158, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 19611, "control": 9695, "name": "Number of orders", "total": 19530, "treatment": 9835, "type": "long"}, {"adjusted": 162, "control": null, "name": "Uplift", "total": null, "treatment": 81, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0066, "control": 0.0065, "name": "Conversion rate", "total": 0.0066, "treatment": 0.0066, "type": "percentage"}, {"adjusted": 0.0083, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0083, "type": "percentage"}, {"adjusted": null, "control": 0.0064, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0129, "treatment": 0.0065, "type": "percentage"}, {"adjusted": null, "control": 0.0067, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0134, "treatment": 0.0067, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0307, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.049, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1508799, "control": 796846, "name": "Revenue", "total": 1553518, "treatment": 756672, "type": "currency"}, {"adjusted": null, "control": 82.19, "name": "AOV", "total": 79.55, "treatment": 76.94, "type": "currency"}, {"adjusted": -89708, "control": null, "name": "Uplift", "total": null, "treatment": -44989, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0083, "actualQuantityUplift": 81.41, "adjustedQuantityUplift": 162.34, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.2800373800958733}}]