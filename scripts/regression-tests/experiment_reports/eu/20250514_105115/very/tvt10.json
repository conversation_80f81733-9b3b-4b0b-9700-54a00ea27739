[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-10-05", "experimentId": "tvt10", "experimentState": "finished", "isSeries": "false", "metric": "impressions", "orderType": "conversions", "siteKey": "very", "startDate": "2022-09-21"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Experiment Sessions", "total": 0, "treatment": 0, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Experiment split", "total": null, "treatment": 0.0, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 44093861, "name": "Taggstar Experiment Impressions", "total": 88187760, "treatment": 44093899, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 35781703, "name": "Taggstar Message Impressions", "total": 71719453, "treatment": 35937750, "type": "long"}, {"adjusted": null, "control": 0.8115, "name": "Taggstar coverage", "total": null, "treatment": 0.815, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 493512, "control": 247897, "name": "Number of orders", "total": 494653, "treatment": 246756, "type": "long"}, {"adjusted": -2282, "control": null, "name": "Uplift", "total": null, "treatment": -1141, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0056, "control": 0.0056, "name": "Conversion rate", "total": 0.0056, "treatment": 0.0056, "type": "percentage"}, {"adjusted": -0.0046, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0046, "type": "percentage"}, {"adjusted": null, "control": 0.0056, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0112, "treatment": 0.0056, "type": "percentage"}, {"adjusted": null, "control": 0.0056, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0113, "treatment": 0.0056, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0124, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0033, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 76868414, "control": 38581618, "name": "Revenue", "total": 77015842, "treatment": 38434224, "type": "currency"}, {"adjusted": null, "control": 155.64, "name": "AOV", "total": 155.7, "treatment": 155.76, "type": "currency"}, {"adjusted": -294856, "control": null, "name": "Uplift", "total": null, "treatment": -147428, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0046, "actualQuantityUplift": -1141.21, "adjustedQuantityUplift": -2282.43, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.9481514687519903}}]