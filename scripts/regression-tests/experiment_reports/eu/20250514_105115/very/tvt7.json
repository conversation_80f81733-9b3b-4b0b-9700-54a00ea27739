[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-08-16", "experimentId": "tvt7", "experimentState": "finished", "isSeries": "false", "metric": "impressions", "orderType": "conversions", "siteKey": "very", "startDate": "2022-07-26"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Experiment Sessions", "total": 0, "treatment": 0, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Experiment split", "total": null, "treatment": 0.0, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 58950640, "name": "Taggstar Experiment Impressions", "total": 117692070, "treatment": 58741430, "type": "long"}, {"adjusted": null, "control": 0.5009, "name": "Experiment split", "total": null, "treatment": 0.4991, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 46473797, "name": "Taggstar Message Impressions", "total": 92924752, "treatment": 46450955, "type": "long"}, {"adjusted": null, "control": 0.7884, "name": "Taggstar coverage", "total": null, "treatment": 0.7908, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 634838, "control": 318655, "name": "Number of orders", "total": 635510, "treatment": 316855, "type": "long"}, {"adjusted": -1341, "control": null, "name": "Uplift", "total": null, "treatment": -669, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0054, "control": 0.0054, "name": "Conversion rate", "total": 0.0054, "treatment": 0.0054, "type": "percentage"}, {"adjusted": -0.0021, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0021, "type": "percentage"}, {"adjusted": null, "control": 0.0054, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0108, "treatment": 0.0054, "type": "percentage"}, {"adjusted": null, "control": 0.0054, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0108, "treatment": 0.0054, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.009, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0048, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 94368037, "control": 47264587, "name": "Revenue", "total": 94364731, "treatment": 47100144, "type": "currency"}, {"adjusted": null, "control": 148.33, "name": "AOV", "total": 148.49, "treatment": 148.65, "type": "currency"}, {"adjusted": 6599, "control": null, "name": "Uplift", "total": null, "treatment": 3294, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0021, "actualQuantityUplift": -669.12, "adjustedQuantityUplift": -1340.63, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8004221151398203}}]