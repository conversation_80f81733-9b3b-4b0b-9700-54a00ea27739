[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-04-13", "experimentId": "TvC2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "gymkingcom", "startDate": "2023-03-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 305091, "name": "Taggstar Experiment Sessions", "total": 611957, "treatment": 306866, "type": "long"}, {"adjusted": null, "control": 0.4985, "name": "Experiment split", "total": null, "treatment": 0.5015, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1760864, "name": "Taggstar Experiment Impressions", "total": 3522532, "treatment": 1761668, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 1518680, "treatment": 1518680, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8621, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 15042, "control": 7556, "name": "Number of orders", "total": 15099, "treatment": 7543, "type": "long"}, {"adjusted": -114, "control": null, "name": "Uplift", "total": null, "treatment": -57, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0246, "control": 0.0248, "name": "Conversion rate", "total": 0.0247, "treatment": 0.0246, "type": "percentage"}, {"adjusted": -0.0075, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0075, "type": "percentage"}, {"adjusted": null, "control": 0.0242, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0482, "treatment": 0.024, "type": "percentage"}, {"adjusted": null, "control": 0.0253, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0504, "treatment": 0.0251, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0508, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0377, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1313112, "control": 803655, "name": "Revenue", "total": 1462115, "treatment": 658460, "type": "currency"}, {"adjusted": null, "control": 106.36, "name": "AOV", "total": 96.84, "treatment": 87.29, "type": "currency"}, {"adjusted": -298874, "control": null, "name": "Uplift", "total": null, "treatment": -149871, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0075, "actualQuantityUplift": -56.96, "adjustedQuantityUplift": -113.59, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.6801128068063675}}]