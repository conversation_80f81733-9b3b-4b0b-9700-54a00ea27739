[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-02-04", "experimentId": "tvt6", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "marksandspencercom", "startDate": "2025-01-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5437460, "name": "Taggstar Experiment Sessions", "total": 10874466, "treatment": 5437006, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 24856822, "name": "Taggstar Experiment Impressions", "total": 49760263, "treatment": 24903441, "type": "long"}, {"adjusted": null, "control": 0.4995, "name": "Experiment split", "total": null, "treatment": 0.5005, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 20781350, "name": "Taggstar Message Impressions", "total": 43011446, "treatment": 22230096, "type": "long"}, {"adjusted": null, "control": 0.836, "name": "Taggstar coverage", "total": null, "treatment": 0.8927, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 564568, "control": 280471, "name": "Number of orders", "total": 562743, "treatment": 282272, "type": "long"}, {"adjusted": 3649, "control": null, "name": "Uplift", "total": null, "treatment": 1824, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0519, "control": 0.0516, "name": "Conversion rate", "total": 0.0517, "treatment": 0.0519, "type": "percentage"}, {"adjusted": 0.0065, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0065, "type": "percentage"}, {"adjusted": null, "control": 0.0514, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1031, "treatment": 0.0517, "type": "percentage"}, {"adjusted": null, "control": 0.0518, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1039, "treatment": 0.0521, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0007, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0138, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 32348013, "control": 16029323, "name": "Revenue", "total": 32202654, "treatment": 16173331, "type": "currency"}, {"adjusted": null, "control": 57.15, "name": "AOV", "total": 57.22, "treatment": 57.3, "type": "currency"}, {"adjusted": 290705, "control": null, "name": "Uplift", "total": null, "treatment": 145346, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0065, "actualQuantityUplift": 1824.42, "adjustedQuantityUplift": 3648.99, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.006251561857359191}}]