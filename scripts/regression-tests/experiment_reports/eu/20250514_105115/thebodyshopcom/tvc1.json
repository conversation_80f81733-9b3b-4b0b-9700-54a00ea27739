[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-01-18", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "thebodyshopcom", "startDate": "2023-12-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 843284, "name": "Taggstar Experiment Sessions", "total": 1686027, "treatment": 842743, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3625004, "name": "Taggstar Experiment Impressions", "total": 7159727, "treatment": 3534723, "type": "long"}, {"adjusted": null, "control": 0.5063, "name": "Experiment split", "total": null, "treatment": 0.4937, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 3437822, "treatment": 3437822, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9726, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 80824, "control": 39308, "name": "Number of orders", "total": 79707, "treatment": 40399, "type": "long"}, {"adjusted": 2233, "control": null, "name": "Uplift", "total": null, "treatment": 1116, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0479, "control": 0.0466, "name": "Conversion rate", "total": 0.0473, "treatment": 0.0479, "type": "percentage"}, {"adjusted": 0.0284, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0284, "type": "percentage"}, {"adjusted": null, "control": 0.0462, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0936, "treatment": 0.0475, "type": "percentage"}, {"adjusted": null, "control": 0.0471, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0955, "treatment": 0.0484, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0089, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0483, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3078036, "control": 1480532, "name": "Revenue", "total": 3019056, "treatment": 1538524, "type": "currency"}, {"adjusted": null, "control": 37.66, "name": "AOV", "total": 37.88, "treatment": 38.08, "type": "currency"}, {"adjusted": 117922, "control": null, "name": "Uplift", "total": null, "treatment": 58942, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0284, "actualQuantityUplift": 1116.22, "adjustedQuantityUplift": 2233.15, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 2.5403948397799026e-05}}]