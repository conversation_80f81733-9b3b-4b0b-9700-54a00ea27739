[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-02-01", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "impressions", "orderType": "conversions", "siteKey": "bootscom", "startDate": "2023-01-18"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6757612, "name": "Taggstar Experiment Sessions", "total": 13517690, "treatment": 6760078, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 18851786, "name": "Taggstar Experiment Impressions", "total": 37719442, "treatment": 18867656, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 16942469, "name": "Taggstar Message Impressions", "total": 34326268, "treatment": 17383799, "type": "long"}, {"adjusted": null, "control": 0.8987, "name": "Taggstar coverage", "total": null, "treatment": 0.9214, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 426549, "control": 213958, "name": "Number of orders", "total": 427322, "treatment": 213364, "type": "long"}, {"adjusted": -1548, "control": null, "name": "Uplift", "total": null, "treatment": -774, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0113, "control": 0.0113, "name": "Conversion rate", "total": 0.0113, "treatment": 0.0113, "type": "percentage"}, {"adjusted": -0.0036, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0036, "type": "percentage"}, {"adjusted": null, "control": 0.0113, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0226, "treatment": 0.0113, "type": "percentage"}, {"adjusted": null, "control": 0.0114, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0228, "treatment": 0.0114, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.012, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0048, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 12908134, "control": 6483463, "name": "Revenue", "total": 12940246, "treatment": 6456782, "type": "currency"}, {"adjusted": null, "control": 30.3, "name": "AOV", "total": 30.28, "treatment": 30.26, "type": "currency"}, {"adjusted": -64250, "control": null, "name": "Uplift", "total": null, "treatment": -32139, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0036, "actualQuantityUplift": -774.12, "adjustedQuantityUplift": -1547.58, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8830697706094753}}]