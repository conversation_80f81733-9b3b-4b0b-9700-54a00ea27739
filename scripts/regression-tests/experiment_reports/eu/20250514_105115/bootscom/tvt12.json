[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-12-23", "experimentId": "tvt12", "experimentState": "finished", "isSeries": "false", "metric": "impressions", "orderType": "conversions", "siteKey": "bootscom", "startDate": "2024-12-04"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 15394005, "name": "Taggstar Experiment Sessions", "total": 30791370, "treatment": 15397365, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 36279333, "name": "Taggstar Experiment Impressions", "total": 72580976, "treatment": 36301643, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 34350883, "name": "Taggstar Message Impressions", "total": 68724908, "treatment": 34374025, "type": "long"}, {"adjusted": null, "control": 0.9468, "name": "Taggstar coverage", "total": null, "treatment": 0.9469, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 961818, "control": 481695, "name": "Number of orders", "total": 962752, "treatment": 481057, "type": "long"}, {"adjusted": -1868, "control": null, "name": "Uplift", "total": null, "treatment": -934, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0133, "control": 0.0133, "name": "Conversion rate", "total": 0.0133, "treatment": 0.0133, "type": "percentage"}, {"adjusted": -0.0019, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0019, "type": "percentage"}, {"adjusted": null, "control": 0.0132, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0265, "treatment": 0.0132, "type": "percentage"}, {"adjusted": null, "control": 0.0133, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0266, "treatment": 0.0133, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0075, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0037, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 35135160, "control": 17610139, "name": "Revenue", "total": 35183119, "treatment": 17572980, "type": "currency"}, {"adjusted": null, "control": 36.56, "name": "AOV", "total": 36.54, "treatment": 36.53, "type": "currency"}, {"adjusted": -95948, "control": null, "name": "Uplift", "total": null, "treatment": -47989, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0019, "actualQuantityUplift": -934.22, "adjustedQuantityUplift": -1867.86, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.831019690134466}}]