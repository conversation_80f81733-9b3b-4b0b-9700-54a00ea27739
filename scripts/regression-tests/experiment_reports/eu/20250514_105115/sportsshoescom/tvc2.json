[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2021-09-27", "experimentId": "tvc2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "sportsshoescom", "startDate": "2020-12-05"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2452961, "name": "Taggstar Experiment Sessions", "total": 46432570, "treatment": 43979609, "type": "long"}, {"adjusted": null, "control": 0.0528, "name": "Experiment split", "total": null, "treatment": 0.9472, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 8266715, "name": "Taggstar Experiment Impressions", "total": 166296391, "treatment": 158029676, "type": "long"}, {"adjusted": null, "control": 0.0497, "name": "Experiment split", "total": null, "treatment": 0.9503, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 90556125, "treatment": 90556125, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.573, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 905602, "control": 43953, "name": "Number of orders", "total": 901713, "treatment": 857760, "type": "long"}, {"adjusted": 73607, "control": null, "name": "Uplift", "total": null, "treatment": 69718, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0195, "control": 0.0179, "name": "Conversion rate", "total": 0.0187, "treatment": 0.0195, "type": "percentage"}, {"adjusted": 0.0885, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0885, "type": "percentage"}, {"adjusted": null, "control": 0.0178, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0372, "treatment": 0.0195, "type": "percentage"}, {"adjusted": null, "control": 0.0181, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0376, "treatment": 0.0195, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0762, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.101, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 0, "control": 0, "name": "Revenue", "total": 0, "treatment": 0, "type": "currency"}, {"adjusted": null, "control": 0.0, "name": "AOV", "total": 0.0, "treatment": 0.0, "type": "currency"}, {"adjusted": 0, "control": null, "name": "Uplift", "total": null, "treatment": 0, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0885, "actualQuantityUplift": 69718.22, "adjustedQuantityUplift": 73606.75, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 4.181783772254266e-74}}]