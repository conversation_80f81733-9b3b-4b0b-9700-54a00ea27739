[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-11-22", "experimentId": "tvt6", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "sportsshoescom", "startDate": "2023-11-02"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1426202, "name": "Taggstar Experiment Sessions", "total": 2851502, "treatment": 1425300, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6065936, "name": "Taggstar Experiment Impressions", "total": 12141291, "treatment": 6075355, "type": "long"}, {"adjusted": null, "control": 0.4996, "name": "Experiment split", "total": null, "treatment": 0.5004, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 5279230, "name": "Taggstar Message Impressions", "total": 10567072, "treatment": 5287842, "type": "long"}, {"adjusted": null, "control": 0.8703, "name": "Taggstar coverage", "total": null, "treatment": 0.8704, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 72637, "control": 37032, "name": "Number of orders", "total": 73339, "treatment": 36307, "type": "long"}, {"adjusted": -1404, "control": null, "name": "Uplift", "total": null, "treatment": -702, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0255, "control": 0.026, "name": "Conversion rate", "total": 0.0257, "treatment": 0.0255, "type": "percentage"}, {"adjusted": -0.019, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.019, "type": "percentage"}, {"adjusted": null, "control": 0.0257, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0509, "treatment": 0.0252, "type": "percentage"}, {"adjusted": null, "control": 0.0262, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.052, "treatment": 0.0257, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0386, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0011, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 7290798, "control": 3695160, "name": "Revenue", "total": 7339406, "treatment": 3644246, "type": "currency"}, {"adjusted": null, "control": 99.78, "name": "AOV", "total": 100.08, "treatment": 100.37, "type": "currency"}, {"adjusted": -97185, "control": null, "name": "Uplift", "total": null, "treatment": -48577, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.019, "actualQuantityUplift": -701.58, "adjustedQuantityUplift": -1403.6, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9956734732254368}}]