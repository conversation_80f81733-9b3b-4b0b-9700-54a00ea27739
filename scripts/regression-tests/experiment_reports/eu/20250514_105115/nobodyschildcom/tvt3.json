[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-12-08", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "nobodyschildcom", "startDate": "2023-11-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 340945, "name": "Taggstar Experiment Sessions", "total": 682264, "treatment": 341319, "type": "long"}, {"adjusted": null, "control": 0.4997, "name": "Experiment split", "total": null, "treatment": 0.5003, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2400374, "name": "Taggstar Experiment Impressions", "total": 4799382, "treatment": 2399008, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 2032851, "name": "Taggstar Message Impressions", "total": 4066684, "treatment": 2033833, "type": "long"}, {"adjusted": null, "control": 0.8469, "name": "Taggstar coverage", "total": null, "treatment": 0.8478, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 22869, "control": 11522, "name": "Number of orders", "total": 22963, "treatment": 11441, "type": "long"}, {"adjusted": -187, "control": null, "name": "Uplift", "total": null, "treatment": -94, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0335, "control": 0.0338, "name": "Conversion rate", "total": 0.0337, "treatment": 0.0335, "type": "percentage"}, {"adjusted": -0.0081, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0081, "type": "percentage"}, {"adjusted": null, "control": 0.0332, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0661, "treatment": 0.0329, "type": "percentage"}, {"adjusted": null, "control": 0.0344, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0685, "treatment": 0.0341, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0432, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0282, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2259004, "control": 1145538, "name": "Revenue", "total": 2275660, "treatment": 1130121, "type": "currency"}, {"adjusted": null, "control": 99.42, "name": "AOV", "total": 99.1, "treatment": 98.78, "type": "currency"}, {"adjusted": -33329, "control": null, "name": "Uplift", "total": null, "treatment": -16674, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0081, "actualQuantityUplift": -93.64, "adjustedQuantityUplift": -187.18, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7350826414236236}}]