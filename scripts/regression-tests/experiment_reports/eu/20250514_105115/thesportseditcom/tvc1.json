[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-11-05", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "thesportseditcom", "startDate": "2024-10-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 203539, "name": "Taggstar Experiment Sessions", "total": 407793, "treatment": 204254, "type": "long"}, {"adjusted": null, "control": 0.4991, "name": "Experiment split", "total": null, "treatment": 0.5009, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 464614, "name": "Taggstar Experiment Impressions", "total": 912541, "treatment": 447927, "type": "long"}, {"adjusted": null, "control": 0.5091, "name": "Experiment split", "total": null, "treatment": 0.4909, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 382541, "treatment": 382541, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.854, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 5476, "control": 2789, "name": "Number of orders", "total": 5532, "treatment": 2743, "type": "long"}, {"adjusted": -111, "control": null, "name": "Uplift", "total": null, "treatment": -56, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0134, "control": 0.0137, "name": "Conversion rate", "total": 0.0136, "treatment": 0.0134, "type": "percentage"}, {"adjusted": -0.0199, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0199, "type": "percentage"}, {"adjusted": null, "control": 0.0132, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0261, "treatment": 0.0129, "type": "percentage"}, {"adjusted": null, "control": 0.0142, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0281, "treatment": 0.0139, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0899, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0554, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 501325, "control": 252521, "name": "Revenue", "total": 503623, "treatment": 251102, "type": "currency"}, {"adjusted": null, "control": 90.54, "name": "AOV", "total": 91.04, "treatment": 91.54, "type": "currency"}, {"adjusted": -4604, "control": null, "name": "Uplift", "total": null, "treatment": -2306, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0199, "actualQuantityUplift": -55.8, "adjustedQuantityUplift": -111.4, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7745743653866473}}]