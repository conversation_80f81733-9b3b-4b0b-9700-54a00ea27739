[{"category": "Overall", "metaData": {"categoryEnabled": "true", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2018-01-10", "experimentId": "experiment7", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "argoscouk", "startDate": "2017-11-13"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6952453, "name": "Taggstar Experiment Sessions", "total": 137864790, "treatment": 130912337, "type": "long"}, {"adjusted": null, "control": 0.0504, "name": "Experiment split", "total": null, "treatment": 0.9496, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 17166582, "name": "Taggstar Experiment Impressions", "total": 342279654, "treatment": 325113072, "type": "long"}, {"adjusted": null, "control": 0.0502, "name": "Experiment split", "total": null, "treatment": 0.9498, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 307266139, "treatment": 307266139, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9451, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 7582608, "control": 366676, "name": "Number of orders", "total": 7566897, "treatment": 7200221, "type": "long"}, {"adjusted": 311547, "control": null, "name": "Uplift", "total": null, "treatment": 295836, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.055, "control": 0.0527, "name": "Conversion rate", "total": 0.0539, "treatment": 0.055, "type": "percentage"}, {"adjusted": 0.0428, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0428, "type": "percentage"}, {"adjusted": null, "control": 0.0526, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1075, "treatment": 0.055, "type": "percentage"}, {"adjusted": null, "control": 0.0529, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1079, "treatment": 0.055, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0388, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0469, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 531290702, "control": 25841585, "name": "Revenue", "total": 530339560, "treatment": 504497975, "type": "currency"}, {"adjusted": null, "control": 70.48, "name": "AOV", "total": 70.09, "treatment": 70.07, "type": "currency"}, {"adjusted": 18860813, "control": null, "name": "Uplift", "total": null, "treatment": 17909672, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0428, "actualQuantityUplift": 295836.02, "adjustedQuantityUplift": 311547.19, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 8.847192070733766e-149}}]