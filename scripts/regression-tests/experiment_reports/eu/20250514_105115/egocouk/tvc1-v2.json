[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-12-05", "experimentId": "tvc1-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "egocouk", "startDate": "2024-11-05"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 590538, "name": "Taggstar Experiment Sessions", "total": 1181877, "treatment": 591339, "type": "long"}, {"adjusted": null, "control": 0.4997, "name": "Experiment split", "total": null, "treatment": 0.5003, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2444590, "name": "Taggstar Experiment Impressions", "total": 4880154, "treatment": 2435564, "type": "long"}, {"adjusted": null, "control": 0.5009, "name": "Experiment split", "total": null, "treatment": 0.4991, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 2142803, "treatment": 2142803, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8798, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 17140, "control": 8160, "name": "Number of orders", "total": 16736, "treatment": 8576, "type": "long"}, {"adjusted": 809, "control": null, "name": "Uplift", "total": null, "treatment": 405, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0145, "control": 0.0138, "name": "Conversion rate", "total": 0.0142, "treatment": 0.0145, "type": "percentage"}, {"adjusted": 0.0496, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0496, "type": "percentage"}, {"adjusted": null, "control": 0.0135, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0277, "treatment": 0.0142, "type": "percentage"}, {"adjusted": null, "control": 0.0141, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0289, "treatment": 0.0148, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0058, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0952, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1058412, "control": 500754, "name": "Revenue", "total": 1030319, "treatment": 529565, "type": "currency"}, {"adjusted": null, "control": 61.37, "name": "AOV", "total": 61.56, "treatment": 61.75, "type": "currency"}, {"adjusted": 56225, "control": null, "name": "Uplift", "total": null, "treatment": 28131, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0496, "actualQuantityUplift": 404.93, "adjustedQuantityUplift": 809.32, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0008151986628629591}}]