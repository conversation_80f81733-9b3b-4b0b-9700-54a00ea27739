[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-11-29", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2022-11-22"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5546782, "name": "Taggstar Experiment Sessions", "total": 11095217, "treatment": 5548435, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 37398241, "name": "Taggstar Experiment Impressions", "total": 74835303, "treatment": 37437062, "type": "long"}, {"adjusted": null, "control": 0.4997, "name": "Experiment split", "total": null, "treatment": 0.5003, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 37109035, "name": "Taggstar Message Impressions", "total": 74223425, "treatment": 37114390, "type": "long"}, {"adjusted": null, "control": 0.9923, "name": "Taggstar coverage", "total": null, "treatment": 0.9914, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 356417, "control": 178287, "name": "Number of orders", "total": 356522, "treatment": 178235, "type": "long"}, {"adjusted": -210, "control": null, "name": "Uplift", "total": null, "treatment": -105, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0321, "control": 0.0321, "name": "Conversion rate", "total": 0.0321, "treatment": 0.0321, "type": "percentage"}, {"adjusted": -0.0006, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0006, "type": "percentage"}, {"adjusted": null, "control": 0.032, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.064, "treatment": 0.032, "type": "percentage"}, {"adjusted": null, "control": 0.0323, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0646, "treatment": 0.0323, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0097, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0086, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 52220122, "control": 25970359, "name": "Revenue", "total": 52084310, "treatment": 26113951, "type": "currency"}, {"adjusted": null, "control": 145.67, "name": "AOV", "total": 146.09, "treatment": 146.51, "type": "currency"}, {"adjusted": 271665, "control": null, "name": "Uplift", "total": null, "treatment": 135853, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0006, "actualQuantityUplift": -105.13, "adjustedQuantityUplift": -210.23, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.5710091072816303}}]