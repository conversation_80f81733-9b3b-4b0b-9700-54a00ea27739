[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-03-28", "experimentId": "tvt11", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2024-03-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2948034, "name": "Taggstar Experiment Sessions", "total": 5904180, "treatment": 2956146, "type": "long"}, {"adjusted": null, "control": 0.4993, "name": "Experiment split", "total": null, "treatment": 0.5007, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 17263000, "name": "Taggstar Experiment Impressions", "total": 34546930, "treatment": 17283930, "type": "long"}, {"adjusted": null, "control": 0.4997, "name": "Experiment split", "total": null, "treatment": 0.5003, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 16620135, "name": "Taggstar Message Impressions", "total": 33265850, "treatment": 16645715, "type": "long"}, {"adjusted": null, "control": 0.9628, "name": "Taggstar coverage", "total": null, "treatment": 0.9631, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 127519, "control": 63165, "name": "Number of orders", "total": 127012, "treatment": 63847, "type": "long"}, {"adjusted": 1015, "control": null, "name": "Uplift", "total": null, "treatment": 508, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0216, "control": 0.0214, "name": "Conversion rate", "total": 0.0215, "treatment": 0.0216, "type": "percentage"}, {"adjusted": 0.008, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.008, "type": "percentage"}, {"adjusted": null, "control": 0.0213, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0427, "treatment": 0.0214, "type": "percentage"}, {"adjusted": null, "control": 0.0216, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0434, "treatment": 0.0218, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0074, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0237, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 15734814, "control": 7793218, "name": "Revenue", "total": 15671434, "treatment": 7878216, "type": "currency"}, {"adjusted": null, "control": 123.38, "name": "AOV", "total": 123.39, "treatment": 123.39, "type": "currency"}, {"adjusted": 126934, "control": null, "name": "Uplift", "total": null, "treatment": 63554, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.008, "actualQuantityUplift": 508.19, "adjustedQuantityUplift": 1014.99, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.07499458474645587}}]