[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-06-01", "experimentId": "tvt9", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-05-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2918119, "name": "Taggstar Experiment Sessions", "total": 5839234, "treatment": 2921115, "type": "long"}, {"adjusted": null, "control": 0.4997, "name": "Experiment split", "total": null, "treatment": 0.5003, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 18552256, "name": "Taggstar Experiment Impressions", "total": 37146038, "treatment": 18593782, "type": "long"}, {"adjusted": null, "control": 0.4994, "name": "Experiment split", "total": null, "treatment": 0.5006, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 18278659, "name": "Taggstar Message Impressions", "total": 36603290, "treatment": 18324631, "type": "long"}, {"adjusted": null, "control": 0.9853, "name": "Taggstar coverage", "total": null, "treatment": 0.9855, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 135594, "control": 66280, "name": "Number of orders", "total": 134112, "treatment": 67832, "type": "long"}, {"adjusted": 2966, "control": null, "name": "Uplift", "total": null, "treatment": 1484, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0232, "control": 0.0227, "name": "Conversion rate", "total": 0.023, "treatment": 0.0232, "type": "percentage"}, {"adjusted": 0.0224, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0224, "type": "percentage"}, {"adjusted": null, "control": 0.0225, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0456, "treatment": 0.023, "type": "percentage"}, {"adjusted": null, "control": 0.0229, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0463, "treatment": 0.0234, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0072, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0378, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 20240470, "control": 9924785, "name": "Revenue", "total": 20050212, "treatment": 10125427, "type": "currency"}, {"adjusted": null, "control": 149.74, "name": "AOV", "total": 149.5, "treatment": 149.27, "type": "currency"}, {"adjusted": 380710, "control": null, "name": "Uplift", "total": null, "treatment": 190453, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0224, "actualQuantityUplift": 1483.95, "adjustedQuantityUplift": 2966.38, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 2.0888213372089112e-05}}]