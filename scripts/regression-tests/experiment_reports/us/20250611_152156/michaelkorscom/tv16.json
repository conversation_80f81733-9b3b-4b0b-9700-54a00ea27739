[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-12-05", "experimentId": "tv16", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2024-10-15"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 21634269, "name": "Taggstar Experiment Sessions", "total": 43257509, "treatment": 21623240, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 124701762, "name": "Taggstar Experiment Impressions", "total": 249514762, "treatment": 124813000, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 120189707, "name": "Taggstar Message Impressions", "total": 240493582, "treatment": 120303875, "type": "long"}, {"adjusted": null, "control": 0.9638, "name": "Taggstar coverage", "total": null, "treatment": 0.9639, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 976725, "control": 488743, "name": "Number of orders", "total": 976981, "treatment": 488238, "type": "long"}, {"adjusted": -512, "control": null, "name": "Uplift", "total": null, "treatment": -256, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0226, "control": 0.0226, "name": "Conversion rate", "total": 0.0226, "treatment": 0.0226, "type": "percentage"}, {"adjusted": -0.0005, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0005, "type": "percentage"}, {"adjusted": null, "control": 0.0225, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.045, "treatment": 0.0225, "type": "percentage"}, {"adjusted": null, "control": 0.0227, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0453, "treatment": 0.0226, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0061, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.005, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 123954572, "control": 61441584, "name": "Revenue", "total": 123403068, "treatment": 61961484, "type": "currency"}, {"adjusted": null, "control": 125.71, "name": "AOV", "total": 126.31, "treatment": 126.91, "type": "currency"}, {"adjusted": 1102727, "control": null, "name": "Uplift", "total": null, "treatment": 551223, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0005, "actualQuantityUplift": -255.84, "adjustedQuantityUplift": -511.81, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.6032925843439324}}]