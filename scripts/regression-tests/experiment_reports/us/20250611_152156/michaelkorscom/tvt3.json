[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-12-08", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2022-12-01"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2988308, "name": "Taggstar Experiment Sessions", "total": 5974066, "treatment": 2985758, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 16906647, "name": "Taggstar Experiment Impressions", "total": 33753706, "treatment": 16847059, "type": "long"}, {"adjusted": null, "control": 0.5009, "name": "Experiment split", "total": null, "treatment": 0.4991, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 16739594, "name": "Taggstar Message Impressions", "total": 33427740, "treatment": 16688146, "type": "long"}, {"adjusted": null, "control": 0.9901, "name": "Taggstar coverage", "total": null, "treatment": 0.9906, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 142297, "control": 72153, "name": "Number of orders", "total": 143271, "treatment": 71118, "type": "long"}, {"adjusted": -1948, "control": null, "name": "Uplift", "total": null, "treatment": -973, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0238, "control": 0.0241, "name": "Conversion rate", "total": 0.024, "treatment": 0.0238, "type": "percentage"}, {"adjusted": -0.0135, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0135, "type": "percentage"}, {"adjusted": null, "control": 0.024, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0476, "treatment": 0.0236, "type": "percentage"}, {"adjusted": null, "control": 0.0243, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0483, "treatment": 0.024, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0277, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0009, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 21572783, "control": 11002500, "name": "Revenue", "total": 21784288, "treatment": 10781787, "type": "currency"}, {"adjusted": null, "control": 152.49, "name": "AOV", "total": 152.05, "treatment": 151.6, "type": "currency"}, {"adjusted": -422829, "control": null, "name": "Uplift", "total": null, "treatment": -211324, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0135, "actualQuantityUplift": -973.43, "adjustedQuantityUplift": -1947.69, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9953962923376808}}]