[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-07-28", "experimentId": "tvc", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2022-06-24"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6453055, "name": "Taggstar Experiment Sessions", "total": 12908647, "treatment": 6455592, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 40920351, "name": "Taggstar Experiment Impressions", "total": 82176952, "treatment": 41256601, "type": "long"}, {"adjusted": null, "control": 0.498, "name": "Experiment split", "total": null, "treatment": 0.502, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 40071128, "treatment": 40071128, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9713, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 311681, "control": 144304, "name": "Number of orders", "total": 300175, "treatment": 155871, "type": "long"}, {"adjusted": 23016, "control": null, "name": "Uplift", "total": null, "treatment": 11510, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0241, "control": 0.0224, "name": "Conversion rate", "total": 0.0233, "treatment": 0.0241, "type": "percentage"}, {"adjusted": 0.0797, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0797, "type": "percentage"}, {"adjusted": null, "control": 0.0222, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0463, "treatment": 0.024, "type": "percentage"}, {"adjusted": null, "control": 0.0225, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0467, "treatment": 0.0243, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.069, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0906, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 45536813, "control": 21141208, "name": "Revenue", "total": 43914089, "treatment": 22772881, "type": "currency"}, {"adjusted": null, "control": 146.5, "name": "AOV", "total": 146.29, "treatment": 146.1, "type": "currency"}, {"adjusted": 3246085, "control": null, "name": "Uplift", "total": null, "treatment": 1623361, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0797, "actualQuantityUplift": 11510.27, "adjustedQuantityUplift": 23016.01, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.5263177735430682e-100}}]