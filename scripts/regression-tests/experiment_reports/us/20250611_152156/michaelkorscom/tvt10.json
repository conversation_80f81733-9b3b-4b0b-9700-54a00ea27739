[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-10-25", "experimentId": "tvt10", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-10-12"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2930653, "name": "Taggstar Experiment Sessions", "total": 5863660, "treatment": 2933007, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 18144237, "name": "Taggstar Experiment Impressions", "total": 36269192, "treatment": 18124955, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 16593168, "name": "Taggstar Message Impressions", "total": 33163744, "treatment": 16570576, "type": "long"}, {"adjusted": null, "control": 0.9145, "name": "Taggstar coverage", "total": null, "treatment": 0.9142, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 83468, "control": 40692, "name": "Number of orders", "total": 82443, "treatment": 41751, "type": "long"}, {"adjusted": 2052, "control": null, "name": "Uplift", "total": null, "treatment": 1026, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0142, "control": 0.0139, "name": "Conversion rate", "total": 0.0141, "treatment": 0.0142, "type": "percentage"}, {"adjusted": 0.0252, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0252, "type": "percentage"}, {"adjusted": null, "control": 0.0138, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0279, "treatment": 0.0141, "type": "percentage"}, {"adjusted": null, "control": 0.014, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0284, "treatment": 0.0144, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0057, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.045, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 14675068, "control": 7070002, "name": "Revenue", "total": 14410481, "treatment": 7340480, "type": "currency"}, {"adjusted": null, "control": 173.74, "name": "AOV", "total": 174.79, "treatment": 175.82, "type": "currency"}, {"adjusted": 529386, "control": null, "name": "Uplift", "total": null, "treatment": 264799, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0252, "actualQuantityUplift": 1026.31, "adjustedQuantityUplift": 2051.81, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.00016010430548669692}}]