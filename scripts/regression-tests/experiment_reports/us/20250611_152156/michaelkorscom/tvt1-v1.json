[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2022-10-07", "experimentId": "tvt1-v1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2022-09-27"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2156067, "name": "Taggstar Experiment Sessions", "total": 4312372, "treatment": 2156305, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 12989893, "name": "Taggstar Experiment Impressions", "total": 26032813, "treatment": 13042920, "type": "long"}, {"adjusted": null, "control": 0.499, "name": "Experiment split", "total": null, "treatment": 0.501, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 12772011, "name": "Taggstar Message Impressions", "total": 25592877, "treatment": 12820866, "type": "long"}, {"adjusted": null, "control": 0.9832, "name": "Taggstar coverage", "total": null, "treatment": 0.983, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 84091, "control": 42106, "name": "Number of orders", "total": 84154, "treatment": 42048, "type": "long"}, {"adjusted": -125, "control": null, "name": "Uplift", "total": null, "treatment": -63, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0195, "control": 0.0195, "name": "Conversion rate", "total": 0.0195, "treatment": 0.0195, "type": "percentage"}, {"adjusted": -0.0015, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0015, "type": "percentage"}, {"adjusted": null, "control": 0.0193, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0387, "treatment": 0.0193, "type": "percentage"}, {"adjusted": null, "control": 0.0197, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0394, "treatment": 0.0197, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0202, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0176, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 12689973, "control": 6268509, "name": "Revenue", "total": 12613846, "treatment": 6345337, "type": "currency"}, {"adjusted": null, "control": 148.87, "name": "AOV", "total": 149.89, "treatment": 150.91, "type": "currency"}, {"adjusted": 152263, "control": null, "name": "Uplift", "total": null, "treatment": 76136, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0015, "actualQuantityUplift": -62.65, "adjustedQuantityUplift": -125.29, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.5863183469097564}}]