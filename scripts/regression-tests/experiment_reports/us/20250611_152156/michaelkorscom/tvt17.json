[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-01-31", "experimentId": "tvt17", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2025-01-07"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6423676, "name": "Taggstar Experiment Sessions", "total": 12847146, "treatment": 6423470, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 31308855, "name": "Taggstar Experiment Impressions", "total": 62584885, "treatment": 31276030, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 29885128, "name": "Taggstar Message Impressions", "total": 59750609, "treatment": 29865481, "type": "long"}, {"adjusted": null, "control": 0.9545, "name": "Taggstar coverage", "total": null, "treatment": 0.9549, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 195853, "control": 98264, "name": "Number of orders", "total": 196189, "treatment": 97925, "type": "long"}, {"adjusted": -672, "control": null, "name": "Uplift", "total": null, "treatment": -336, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0152, "control": 0.0153, "name": "Conversion rate", "total": 0.0153, "treatment": 0.0152, "type": "percentage"}, {"adjusted": -0.0034, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0034, "type": "percentage"}, {"adjusted": null, "control": 0.0152, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0304, "treatment": 0.0152, "type": "percentage"}, {"adjusted": null, "control": 0.0154, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0307, "treatment": 0.0153, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0157, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.009, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 23879247, "control": 12037267, "name": "Revenue", "total": 23976699, "treatment": 11939432, "type": "currency"}, {"adjusted": null, "control": 122.5, "name": "AOV", "total": 122.21, "treatment": 121.92, "type": "currency"}, {"adjusted": -194902, "control": null, "name": "Uplift", "total": null, "treatment": -97450, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0034, "actualQuantityUplift": -335.85, "adjustedQuantityUplift": -671.71, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7775987321860196}}]