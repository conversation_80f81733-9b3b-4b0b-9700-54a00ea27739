[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-09-05", "experimentId": "tvt13", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2024-08-22"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4116722, "name": "Taggstar Experiment Sessions", "total": 8232630, "treatment": 4115908, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 24868552, "name": "Taggstar Experiment Impressions", "total": 49725144, "treatment": 24856592, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 23926345, "name": "Taggstar Message Impressions", "total": 47840588, "treatment": 23914243, "type": "long"}, {"adjusted": null, "control": 0.9621, "name": "Taggstar coverage", "total": null, "treatment": 0.9621, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 197810, "control": 99704, "name": "Number of orders", "total": 198599, "treatment": 98895, "type": "long"}, {"adjusted": -1579, "control": null, "name": "Uplift", "total": null, "treatment": -789, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.024, "control": 0.0242, "name": "Conversion rate", "total": 0.0241, "treatment": 0.024, "type": "percentage"}, {"adjusted": -0.0079, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0079, "type": "percentage"}, {"adjusted": null, "control": 0.0241, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.048, "treatment": 0.0239, "type": "percentage"}, {"adjusted": null, "control": 0.0244, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0485, "treatment": 0.0242, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.02, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0043, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 24135609, "control": 12078601, "name": "Revenue", "total": 24145212, "treatment": 12066611, "type": "currency"}, {"adjusted": null, "control": 121.14, "name": "AOV", "total": 121.58, "treatment": 122.01, "type": "currency"}, {"adjusted": -19206, "control": null, "name": "Uplift", "total": null, "treatment": -9602, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0079, "actualQuantityUplift": -789.29, "adjustedQuantityUplift": -1578.73, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9635171862656596}}]