[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-02-09", "experimentId": "TVT6", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2023-01-28"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2784294, "name": "Taggstar Experiment Sessions", "total": 5578574, "treatment": 2794280, "type": "long"}, {"adjusted": null, "control": 0.4991, "name": "Experiment split", "total": null, "treatment": 0.5009, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 15561248, "name": "Taggstar Experiment Impressions", "total": 31305367, "treatment": 15744119, "type": "long"}, {"adjusted": null, "control": 0.4971, "name": "Experiment split", "total": null, "treatment": 0.5029, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 15320417, "name": "Taggstar Message Impressions", "total": 30815486, "treatment": 15495069, "type": "long"}, {"adjusted": null, "control": 0.9845, "name": "Taggstar coverage", "total": null, "treatment": 0.9842, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 109638, "control": 53891, "name": "Number of orders", "total": 108808, "treatment": 54917, "type": "long"}, {"adjusted": 1662, "control": null, "name": "Uplift", "total": null, "treatment": 833, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0197, "control": 0.0194, "name": "Conversion rate", "total": 0.0195, "treatment": 0.0197, "type": "percentage"}, {"adjusted": 0.0154, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0154, "type": "percentage"}, {"adjusted": null, "control": 0.0192, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0387, "treatment": 0.0195, "type": "percentage"}, {"adjusted": null, "control": 0.0195, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0393, "treatment": 0.0198, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0014, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0324, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 16011351, "control": 7879980, "name": "Revenue", "total": 15899986, "treatment": 8020006, "type": "currency"}, {"adjusted": null, "control": 146.22, "name": "AOV", "total": 146.13, "treatment": 146.04, "type": "currency"}, {"adjusted": 223130, "control": null, "name": "Uplift", "total": null, "treatment": 111765, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0154, "actualQuantityUplift": 832.72, "adjustedQuantityUplift": 1662.46, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.005465231928526169}}]