[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-10-15", "experimentId": "tvt15", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startDate": "2024-09-25"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4611992, "name": "Taggstar Experiment Sessions", "total": 9225138, "treatment": 4613146, "type": "long"}, {"adjusted": null, "control": 0.4999, "name": "Experiment split", "total": null, "treatment": 0.5001, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 26730233, "name": "Taggstar Experiment Impressions", "total": 53343730, "treatment": 26613497, "type": "long"}, {"adjusted": null, "control": 0.5011, "name": "Experiment split", "total": null, "treatment": 0.4989, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 26116507, "name": "Taggstar Message Impressions", "total": 52113417, "treatment": 25996910, "type": "long"}, {"adjusted": null, "control": 0.977, "name": "Taggstar coverage", "total": null, "treatment": 0.9768, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 171869, "control": 86623, "name": "Number of orders", "total": 172568, "treatment": 85945, "type": "long"}, {"adjusted": -1399, "control": null, "name": "Uplift", "total": null, "treatment": -700, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0186, "control": 0.0188, "name": "Conversion rate", "total": 0.0187, "treatment": 0.0186, "type": "percentage"}, {"adjusted": -0.0081, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0081, "type": "percentage"}, {"adjusted": null, "control": 0.0187, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0372, "treatment": 0.0185, "type": "percentage"}, {"adjusted": null, "control": 0.0189, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0377, "treatment": 0.0188, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0211, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0051, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 23605757, "control": 11204583, "name": "Revenue", "total": 23008938, "treatment": 11804355, "type": "currency"}, {"adjusted": null, "control": 129.35, "name": "AOV", "total": 133.33, "treatment": 137.35, "type": "currency"}, {"adjusted": 1193789, "control": null, "name": "Uplift", "total": null, "treatment": 596969, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0081, "actualQuantityUplift": -699.67, "adjustedQuantityUplift": -1399.17, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9554393413207036}}]