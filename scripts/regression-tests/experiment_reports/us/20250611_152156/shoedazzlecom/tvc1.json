[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-05-08", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "shoedazzlecom", "startDate": "2024-03-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 736668, "name": "Taggstar Experiment Sessions", "total": 1471666, "treatment": 734998, "type": "long"}, {"adjusted": null, "control": 0.5006, "name": "Experiment split", "total": null, "treatment": 0.4994, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4044506, "name": "Taggstar Experiment Impressions", "total": 8068183, "treatment": 4023677, "type": "long"}, {"adjusted": null, "control": 0.5013, "name": "Experiment split", "total": null, "treatment": 0.4987, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 3388988, "treatment": 3388988, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8423, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 32193, "control": 15442, "name": "Number of orders", "total": 31520, "treatment": 16078, "type": "long"}, {"adjusted": 1344, "control": null, "name": "Uplift", "total": null, "treatment": 671, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0219, "control": 0.021, "name": "Conversion rate", "total": 0.0214, "treatment": 0.0219, "type": "percentage"}, {"adjusted": 0.0436, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0436, "type": "percentage"}, {"adjusted": null, "control": 0.0206, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0422, "treatment": 0.0215, "type": "percentage"}, {"adjusted": null, "control": 0.0213, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0435, "treatment": 0.0222, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0118, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0763, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3083362, "control": 1481962, "name": "Revenue", "total": 3021893, "treatment": 1539931, "type": "currency"}, {"adjusted": null, "control": 95.97, "name": "AOV", "total": 95.87, "treatment": 95.78, "type": "currency"}, {"adjusted": 122798, "control": null, "name": "Uplift", "total": null, "treatment": 61329, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0436, "actualQuantityUplift": 671.01, "adjustedQuantityUplift": 1343.54, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 6.54135500694909e-05}}]