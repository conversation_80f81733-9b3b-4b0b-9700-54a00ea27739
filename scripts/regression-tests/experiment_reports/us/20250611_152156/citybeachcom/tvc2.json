[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-06-20", "experimentId": "tvc2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "citybeachcom", "startDate": "2024-05-23"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1011845, "name": "Taggstar Experiment Sessions", "total": 2022460, "treatment": 1010615, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4455526, "name": "Taggstar Experiment Impressions", "total": 8914376, "treatment": 4458850, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 3827181, "treatment": 3827181, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8583, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 29912, "control": 14428, "name": "Number of orders", "total": 29375, "treatment": 14947, "type": "long"}, {"adjusted": 1074, "control": null, "name": "Uplift", "total": null, "treatment": 537, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0148, "control": 0.0143, "name": "Conversion rate", "total": 0.0145, "treatment": 0.0148, "type": "percentage"}, {"adjusted": 0.0372, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0372, "type": "percentage"}, {"adjusted": null, "control": 0.014, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0286, "treatment": 0.0146, "type": "percentage"}, {"adjusted": null, "control": 0.0145, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0295, "treatment": 0.015, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0045, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0711, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3448538, "control": 1669330, "name": "Revenue", "total": 3392550, "treatment": 1723220, "type": "currency"}, {"adjusted": null, "control": 115.7, "name": "AOV", "total": 115.49, "treatment": 115.29, "type": "currency"}, {"adjusted": 111907, "control": null, "name": "Uplift", "total": null, "treatment": 55920, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0372, "actualQuantityUplift": 536.54, "adjustedQuantityUplift": 1073.73, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0008014870609155578}}]