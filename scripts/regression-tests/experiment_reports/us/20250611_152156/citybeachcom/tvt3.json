[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-10-16", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "citybeachcom", "startDate": "2024-09-17"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1564566, "name": "Taggstar Experiment Sessions", "total": 3129336, "treatment": 1564770, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6256573, "name": "Taggstar Experiment Impressions", "total": 12549601, "treatment": 6293028, "type": "long"}, {"adjusted": null, "control": 0.4985, "name": "Experiment split", "total": null, "treatment": 0.5015, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 5442742, "name": "Taggstar Message Impressions", "total": 10794661, "treatment": 5351919, "type": "long"}, {"adjusted": null, "control": 0.8699, "name": "Taggstar coverage", "total": null, "treatment": 0.8505, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 51425, "control": 25762, "name": "Number of orders", "total": 51476, "treatment": 25714, "type": "long"}, {"adjusted": -103, "control": null, "name": "Uplift", "total": null, "treatment": -51, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0164, "control": 0.0165, "name": "Conversion rate", "total": 0.0164, "treatment": 0.0164, "type": "percentage"}, {"adjusted": -0.002, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.002, "type": "percentage"}, {"adjusted": null, "control": 0.0163, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0325, "treatment": 0.0162, "type": "percentage"}, {"adjusted": null, "control": 0.0167, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0333, "treatment": 0.0166, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0259, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0225, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 5849759, "control": 2926812, "name": "Revenue", "total": 5851882, "treatment": 2925070, "type": "currency"}, {"adjusted": null, "control": 113.61, "name": "AOV", "total": 113.68, "treatment": 113.75, "type": "currency"}, {"adjusted": -4246, "control": null, "name": "Uplift", "total": null, "treatment": -2123, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.002, "actualQuantityUplift": -51.36, "adjustedQuantityUplift": -102.71, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.5902693739458227}}]