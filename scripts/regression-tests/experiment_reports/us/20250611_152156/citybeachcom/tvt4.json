[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-11-29", "experimentId": "tvt4", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "citybeachcom", "startDate": "2024-11-12"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1485632, "name": "Taggstar Experiment Sessions", "total": 2968160, "treatment": 1482528, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 7236253, "name": "Taggstar Experiment Impressions", "total": 14465313, "treatment": 7229060, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 6752554, "name": "Taggstar Message Impressions", "total": 13496006, "treatment": 6743452, "type": "long"}, {"adjusted": null, "control": 0.9332, "name": "Taggstar coverage", "total": null, "treatment": 0.9328, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 83926, "control": 41967, "name": "Number of orders", "total": 83886, "treatment": 41919, "type": "long"}, {"adjusted": 79, "control": null, "name": "Uplift", "total": null, "treatment": 40, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0283, "control": 0.0282, "name": "Conversion rate", "total": 0.0283, "treatment": 0.0283, "type": "percentage"}, {"adjusted": 0.0009, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0009, "type": "percentage"}, {"adjusted": null, "control": 0.028, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.056, "treatment": 0.028, "type": "percentage"}, {"adjusted": null, "control": 0.0285, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0571, "treatment": 0.0285, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0178, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.02, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 10252109, "control": 5125770, "name": "Revenue", "total": 10246463, "treatment": 5120694, "type": "currency"}, {"adjusted": null, "control": 122.14, "name": "AOV", "total": 122.15, "treatment": 122.16, "type": "currency"}, {"adjusted": 11279, "control": null, "name": "Uplift", "total": null, "treatment": 5634, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0009, "actualQuantityUplift": 39.68, "adjustedQuantityUplift": 79.45, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.4446705544243543}}]