[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-05-22", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "citybeachcom", "startDate": "2024-05-10"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 487092, "name": "Taggstar Experiment Sessions", "total": 975282, "treatment": 488190, "type": "long"}, {"adjusted": null, "control": 0.4994, "name": "Experiment split", "total": null, "treatment": 0.5006, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2166669, "name": "Taggstar Experiment Impressions", "total": 4344453, "treatment": 2177784, "type": "long"}, {"adjusted": null, "control": 0.4987, "name": "Experiment split", "total": null, "treatment": 0.5013, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 1358805, "treatment": 1358805, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.6239, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 20597, "control": 10237, "name": "Number of orders", "total": 20547, "treatment": 10310, "type": "long"}, {"adjusted": 100, "control": null, "name": "Uplift", "total": null, "treatment": 50, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0211, "control": 0.021, "name": "Conversion rate", "total": 0.0211, "treatment": 0.0211, "type": "percentage"}, {"adjusted": 0.0049, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0049, "type": "percentage"}, {"adjusted": null, "control": 0.0206, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0413, "treatment": 0.0207, "type": "percentage"}, {"adjusted": null, "control": 0.0214, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0429, "treatment": 0.0215, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0329, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0441, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2388001, "control": 1192800, "name": "Revenue", "total": 2388145, "treatment": 1195345, "type": "currency"}, {"adjusted": null, "control": 116.52, "name": "AOV", "total": 116.23, "treatment": 115.94, "type": "currency"}, {"adjusted": -289, "control": null, "name": "Uplift", "total": null, "treatment": -145, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0049, "actualQuantityUplift": 49.92, "adjustedQuantityUplift": 99.74, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.36256287720425084}}]