[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-08-21", "experimentId": "tvc2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "fabkidscom", "startDate": "2024-07-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 378921, "name": "Taggstar Experiment Sessions", "total": 757163, "treatment": 378242, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2867751, "name": "Taggstar Experiment Impressions", "total": 5702585, "treatment": 2834834, "type": "long"}, {"adjusted": null, "control": 0.5029, "name": "Experiment split", "total": null, "treatment": 0.4971, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 2772857, "treatment": 2772857, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9781, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 41003, "control": 20016, "name": "Number of orders", "total": 40499, "treatment": 20483, "type": "long"}, {"adjusted": 1007, "control": null, "name": "Uplift", "total": null, "treatment": 503, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0542, "control": 0.0528, "name": "Conversion rate", "total": 0.0535, "treatment": 0.0542, "type": "percentage"}, {"adjusted": 0.0252, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0252, "type": "percentage"}, {"adjusted": null, "control": 0.0521, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1055, "treatment": 0.0534, "type": "percentage"}, {"adjusted": null, "control": 0.0535, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1084, "treatment": 0.0549, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0019, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.053, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2730265, "control": 1349577, "name": "Revenue", "total": 2713485, "treatment": 1363908, "type": "currency"}, {"adjusted": null, "control": 67.42, "name": "AOV", "total": 67.0, "treatment": 66.59, "type": "currency"}, {"adjusted": 33529, "control": null, "name": "Uplift", "total": null, "treatment": 16750, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0252, "actualQuantityUplift": 502.87, "adjustedQuantityUplift": 1006.64, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0050743553936103665}}]