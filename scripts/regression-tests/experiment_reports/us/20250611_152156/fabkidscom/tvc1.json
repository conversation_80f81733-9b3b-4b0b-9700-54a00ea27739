[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-04-26", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "fabkidscom", "startDate": "2024-03-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 150812, "name": "Taggstar Experiment Sessions", "total": 301247, "treatment": 150435, "type": "long"}, {"adjusted": null, "control": 0.5006, "name": "Experiment split", "total": null, "treatment": 0.4994, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1173871, "name": "Taggstar Experiment Impressions", "total": 2338097, "treatment": 1164226, "type": "long"}, {"adjusted": null, "control": 0.5021, "name": "Experiment split", "total": null, "treatment": 0.4979, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 1027866, "treatment": 1027866, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.8829, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 17392, "control": 8827, "name": "Number of orders", "total": 17512, "treatment": 8685, "type": "long"}, {"adjusted": -240, "control": null, "name": "Uplift", "total": null, "treatment": -120, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0577, "control": 0.0585, "name": "Conversion rate", "total": 0.0581, "treatment": 0.0577, "type": "percentage"}, {"adjusted": -0.0136, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0136, "type": "percentage"}, {"adjusted": null, "control": 0.0573, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1139, "treatment": 0.0566, "type": "percentage"}, {"adjusted": null, "control": 0.0597, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1186, "treatment": 0.0589, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0529, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0273, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1490775, "control": 752286, "name": "Revenue", "total": 1496741, "treatment": 744455, "type": "currency"}, {"adjusted": null, "control": 85.23, "name": "AOV", "total": 85.47, "treatment": 85.72, "type": "currency"}, {"adjusted": -11915, "control": null, "name": "Uplift", "total": null, "treatment": -5950, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0136, "actualQuantityUplift": -119.93, "adjustedQuantityUplift": -240.17, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8251148934945072}}]