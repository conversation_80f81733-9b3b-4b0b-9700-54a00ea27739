[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-04-19", "experimentId": "tvt5", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "goprocom", "startDate": "2024-03-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 821231, "name": "Taggstar Experiment Sessions", "total": 1640173, "treatment": 818942, "type": "long"}, {"adjusted": null, "control": 0.5007, "name": "Experiment split", "total": null, "treatment": 0.4993, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2133981, "name": "Taggstar Experiment Impressions", "total": 4256933, "treatment": 2122952, "type": "long"}, {"adjusted": null, "control": 0.5013, "name": "Experiment split", "total": null, "treatment": 0.4987, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 2083201, "name": "Taggstar Message Impressions", "total": 4155339, "treatment": 2072138, "type": "long"}, {"adjusted": null, "control": 0.9762, "name": "Taggstar coverage", "total": null, "treatment": 0.9761, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 28738, "control": 14378, "name": "Number of orders", "total": 28727, "treatment": 14349, "type": "long"}, {"adjusted": 22, "control": null, "name": "Uplift", "total": null, "treatment": 11, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0175, "control": 0.0175, "name": "Conversion rate", "total": 0.0175, "treatment": 0.0175, "type": "percentage"}, {"adjusted": 0.0008, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0008, "type": "percentage"}, {"adjusted": null, "control": 0.0172, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0345, "treatment": 0.0172, "type": "percentage"}, {"adjusted": null, "control": 0.0178, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0356, "treatment": 0.0178, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0312, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0338, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 6734651, "control": 3471064, "name": "Revenue", "total": 6833690, "treatment": 3362626, "type": "currency"}, {"adjusted": null, "control": 241.41, "name": "AOV", "total": 237.88, "treatment": 234.35, "type": "currency"}, {"adjusted": -197803, "control": null, "name": "Uplift", "total": null, "treatment": -98763, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0008, "actualQuantityUplift": 11.08, "adjustedQuantityUplift": 22.18, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.4736818313496614}}]