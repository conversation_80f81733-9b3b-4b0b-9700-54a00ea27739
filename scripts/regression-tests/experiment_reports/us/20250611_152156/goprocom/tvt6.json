[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-11-05", "experimentId": "tvt6", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "goprocom", "startDate": "2024-10-22"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 129379, "name": "Taggstar Experiment Sessions", "total": 259294, "treatment": 129915, "type": "long"}, {"adjusted": null, "control": 0.499, "name": "Experiment split", "total": null, "treatment": 0.501, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 391689, "name": "Taggstar Experiment Impressions", "total": 785417, "treatment": 393728, "type": "long"}, {"adjusted": null, "control": 0.4987, "name": "Experiment split", "total": null, "treatment": 0.5013, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 372443, "name": "Taggstar Message Impressions", "total": 747267, "treatment": 374824, "type": "long"}, {"adjusted": null, "control": 0.9509, "name": "Taggstar coverage", "total": null, "treatment": 0.952, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 5283, "control": 2716, "name": "Number of orders", "total": 5363, "treatment": 2647, "type": "long"}, {"adjusted": -160, "control": null, "name": "Uplift", "total": null, "treatment": -80, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0204, "control": 0.021, "name": "Conversion rate", "total": 0.0207, "treatment": 0.0204, "type": "percentage"}, {"adjusted": -0.0294, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0294, "type": "percentage"}, {"adjusted": null, "control": 0.0202, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0398, "treatment": 0.0196, "type": "percentage"}, {"adjusted": null, "control": 0.0218, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0429, "treatment": 0.0211, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0995, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0461, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1243821, "control": 650019, "name": "Revenue", "total": 1273215, "treatment": 623196, "type": "currency"}, {"adjusted": null, "control": 239.33, "name": "AOV", "total": 237.41, "treatment": 235.43, "type": "currency"}, {"adjusted": -58909, "control": null, "name": "Uplift", "total": null, "treatment": -29515, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0294, "actualQuantityUplift": -80.25, "adjustedQuantityUplift": -160.17, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8654303220784307}}]