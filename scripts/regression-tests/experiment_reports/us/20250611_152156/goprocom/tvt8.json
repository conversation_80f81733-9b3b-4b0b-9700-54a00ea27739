[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-04-16", "experimentId": "tvt8", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "goprocom", "startDate": "2025-03-27"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 183613, "name": "Taggstar Experiment Sessions", "total": 365981, "treatment": 182368, "type": "long"}, {"adjusted": null, "control": 0.5017, "name": "Experiment split", "total": null, "treatment": 0.4983, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 521767, "name": "Taggstar Experiment Impressions", "total": 1040210, "treatment": 518443, "type": "long"}, {"adjusted": null, "control": 0.5016, "name": "Experiment split", "total": null, "treatment": 0.4984, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 482811, "name": "Taggstar Message Impressions", "total": 970498, "treatment": 487687, "type": "long"}, {"adjusted": null, "control": 0.9253, "name": "Taggstar coverage", "total": null, "treatment": 0.9407, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 8728, "control": 4466, "name": "Number of orders", "total": 8815, "treatment": 4349, "type": "long"}, {"adjusted": -174, "control": null, "name": "Uplift", "total": null, "treatment": -87, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0238, "control": 0.0243, "name": "Conversion rate", "total": 0.0241, "treatment": 0.0238, "type": "percentage"}, {"adjusted": -0.0195, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0195, "type": "percentage"}, {"adjusted": null, "control": 0.0236, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0468, "treatment": 0.0231, "type": "percentage"}, {"adjusted": null, "control": 0.025, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0496, "treatment": 0.0245, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0751, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0393, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2053382, "control": 1043170, "name": "Revenue", "total": 2066368, "treatment": 1023198, "type": "currency"}, {"adjusted": null, "control": 233.58, "name": "AOV", "total": 234.41, "treatment": 235.27, "type": "currency"}, {"adjusted": -25884, "control": null, "name": "Uplift", "total": null, "treatment": -12898, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0195, "actualQuantityUplift": -86.72, "adjustedQuantityUplift": -174.03, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8259230713990328}}]