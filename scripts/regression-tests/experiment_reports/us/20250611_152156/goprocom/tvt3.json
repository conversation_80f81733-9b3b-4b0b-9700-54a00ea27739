[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-08-31", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "goprocom", "startDate": "2023-07-18"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1369755, "name": "Taggstar Experiment Sessions", "total": 2738465, "treatment": 1368710, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3175052, "name": "Taggstar Experiment Impressions", "total": 6343721, "treatment": 3168669, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 2510070, "name": "Taggstar Message Impressions", "total": 5013134, "treatment": 2503064, "type": "long"}, {"adjusted": null, "control": 0.7906, "name": "Taggstar coverage", "total": null, "treatment": 0.7899, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 33979, "control": 17316, "name": "Number of orders", "total": 34299, "treatment": 16983, "type": "long"}, {"adjusted": -640, "control": null, "name": "Uplift", "total": null, "treatment": -320, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0124, "control": 0.0126, "name": "Conversion rate", "total": 0.0125, "treatment": 0.0124, "type": "percentage"}, {"adjusted": -0.0185, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0185, "type": "percentage"}, {"adjusted": null, "control": 0.0125, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0247, "treatment": 0.0122, "type": "percentage"}, {"adjusted": null, "control": 0.0128, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0254, "treatment": 0.0126, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0473, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0112, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 9115913, "control": 4637533, "name": "Revenue", "total": 9193750, "treatment": 4556217, "type": "currency"}, {"adjusted": null, "control": 267.82, "name": "AOV", "total": 268.05, "treatment": 268.28, "type": "currency"}, {"adjusted": -155615, "control": null, "name": "Uplift", "total": null, "treatment": -77778, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0185, "actualQuantityUplift": -319.79, "adjustedQuantityUplift": -639.82, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.9589221044235579}}]