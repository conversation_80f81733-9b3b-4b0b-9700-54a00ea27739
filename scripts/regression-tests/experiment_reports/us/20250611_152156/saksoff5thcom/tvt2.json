[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-05-13", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "saksoff5thcom", "startDate": "2025-04-16"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3444690, "name": "Taggstar Experiment Sessions", "total": 6900875, "treatment": 3456185, "type": "long"}, {"adjusted": null, "control": 0.4992, "name": "Experiment split", "total": null, "treatment": 0.5008, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 22983232, "name": "Taggstar Experiment Impressions", "total": 46021394, "treatment": 23038162, "type": "long"}, {"adjusted": null, "control": 0.4994, "name": "Experiment split", "total": null, "treatment": 0.5006, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 18143361, "name": "Taggstar Message Impressions", "total": 36340780, "treatment": 18197419, "type": "long"}, {"adjusted": null, "control": 0.7894, "name": "Taggstar coverage", "total": null, "treatment": 0.7899, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 96605, "control": 47703, "name": "Number of orders", "total": 96086, "treatment": 48383, "type": "long"}, {"adjusted": 1040, "control": null, "name": "Uplift", "total": null, "treatment": 521, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.014, "control": 0.0138, "name": "Conversion rate", "total": 0.0139, "treatment": 0.014, "type": "percentage"}, {"adjusted": 0.0109, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0109, "type": "percentage"}, {"adjusted": null, "control": 0.0137, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0276, "treatment": 0.0139, "type": "percentage"}, {"adjusted": null, "control": 0.014, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0281, "treatment": 0.0141, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0069, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.029, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 26672863, "control": 13213387, "name": "Revenue", "total": 26572034, "treatment": 13358647, "type": "currency"}, {"adjusted": null, "control": 276.99, "name": "AOV", "total": 276.54, "treatment": 276.1, "type": "currency"}, {"adjusted": 201996, "control": null, "name": "Uplift", "total": null, "treatment": 101166, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0109, "actualQuantityUplift": 520.81, "adjustedQuantityUplift": 1039.9, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.045591707419728345}}]