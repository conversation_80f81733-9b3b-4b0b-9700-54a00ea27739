[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-11-21", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "saksoff5thcom", "startDate": "2024-11-04"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2579144, "name": "Taggstar Experiment Sessions", "total": 5160461, "treatment": 2581317, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 17396086, "name": "Taggstar Experiment Impressions", "total": 34759295, "treatment": 17363209, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 10868318, "treatment": 10868318, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.6259, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 81700, "control": 38322, "name": "Number of orders", "total": 79189, "treatment": 40867, "type": "long"}, {"adjusted": 5023, "control": null, "name": "Uplift", "total": null, "treatment": 2513, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0158, "control": 0.0149, "name": "Conversion rate", "total": 0.0153, "treatment": 0.0158, "type": "percentage"}, {"adjusted": 0.0655, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0655, "type": "percentage"}, {"adjusted": null, "control": 0.0147, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0304, "treatment": 0.0157, "type": "percentage"}, {"adjusted": null, "control": 0.015, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.031, "treatment": 0.016, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0449, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0866, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 23387378, "control": 11016034, "name": "Revenue", "total": 22714647, "treatment": 11698613, "type": "currency"}, {"adjusted": null, "control": 287.46, "name": "AOV", "total": 286.84, "treatment": 286.26, "type": "currency"}, {"adjusted": 1346030, "control": null, "name": "Uplift", "total": null, "treatment": 673298, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0655, "actualQuantityUplift": 2512.71, "adjustedQuantityUplift": 5023.31, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.182610027301962e-19}}]