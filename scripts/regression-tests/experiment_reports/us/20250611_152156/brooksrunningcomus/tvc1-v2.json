[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-05-22", "experimentId": "tvc1-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "brooksrunningcomus", "startDate": "2024-04-17"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2074275, "name": "Taggstar Experiment Sessions", "total": 4148239, "treatment": 2073964, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4177837, "name": "Taggstar Experiment Impressions", "total": 8345641, "treatment": 4167804, "type": "long"}, {"adjusted": null, "control": 0.5006, "name": "Experiment split", "total": null, "treatment": 0.4994, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 3983432, "treatment": 3983432, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9558, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 197487, "control": 97192, "name": "Number of orders", "total": 195928, "treatment": 98736, "type": "long"}, {"adjusted": 3117, "control": null, "name": "Uplift", "total": null, "treatment": 1559, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0476, "control": 0.0469, "name": "Conversion rate", "total": 0.0472, "treatment": 0.0476, "type": "percentage"}, {"adjusted": 0.016, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.016, "type": "percentage"}, {"adjusted": null, "control": 0.0466, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0939, "treatment": 0.0473, "type": "percentage"}, {"adjusted": null, "control": 0.0471, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.095, "treatment": 0.0479, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0037, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0285, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 28502063, "control": 14005191, "name": "Revenue", "total": 28255154, "treatment": 14249963, "type": "currency"}, {"adjusted": null, "control": 144.1, "name": "AOV", "total": 144.21, "treatment": 144.32, "type": "currency"}, {"adjusted": 493781, "control": null, "name": "Uplift", "total": null, "treatment": 246872, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.016, "actualQuantityUplift": 1558.57, "adjustedQuantityUplift": 3117.38, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0001545231307733422}}]