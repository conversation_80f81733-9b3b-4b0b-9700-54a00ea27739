[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-04-16", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "brooksrunningcomus", "startDate": "2024-04-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 397995, "name": "Taggstar Experiment Sessions", "total": 797118, "treatment": 399123, "type": "long"}, {"adjusted": null, "control": 0.4993, "name": "Experiment split", "total": null, "treatment": 0.5007, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 825566, "name": "Taggstar Experiment Impressions", "total": 1651225, "treatment": 825659, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 813246, "treatment": 813246, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.985, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 40615, "control": 20408, "name": "Number of orders", "total": 40744, "treatment": 20336, "type": "long"}, {"adjusted": -259, "control": null, "name": "Uplift", "total": null, "treatment": -130, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.051, "control": 0.0513, "name": "Conversion rate", "total": 0.0511, "treatment": 0.051, "type": "percentage"}, {"adjusted": -0.0063, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0063, "type": "percentage"}, {"adjusted": null, "control": 0.0506, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1009, "treatment": 0.0503, "type": "percentage"}, {"adjusted": null, "control": 0.052, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1036, "treatment": 0.0516, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0326, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0206, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 5928026, "control": 2998722, "name": "Revenue", "total": 5966930, "treatment": 2968207, "type": "currency"}, {"adjusted": null, "control": 146.94, "name": "AOV", "total": 146.45, "treatment": 145.96, "type": "currency"}, {"adjusted": -77918, "control": null, "name": "Uplift", "total": null, "treatment": -39014, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0063, "actualQuantityUplift": -129.84, "adjustedQuantityUplift": -259.31, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.7451834857263928}}]