[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-12-12", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "tartecosmeticscom", "startDate": "2024-10-21"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2109436, "name": "Taggstar Experiment Sessions", "total": 4216141, "treatment": 2106705, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 6675922, "name": "Taggstar Experiment Impressions", "total": 13341830, "treatment": 6665908, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 6640137, "treatment": 6640137, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9961, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 407678, "control": 200197, "name": "Number of orders", "total": 403904, "treatment": 203707, "type": "long"}, {"adjusted": 7543, "control": null, "name": "Uplift", "total": null, "treatment": 3769, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0967, "control": 0.0949, "name": "Conversion rate", "total": 0.0958, "treatment": 0.0967, "type": "percentage"}, {"adjusted": 0.0189, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0189, "type": "percentage"}, {"adjusted": null, "control": 0.0945, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1908, "treatment": 0.0963, "type": "percentage"}, {"adjusted": null, "control": 0.0953, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1924, "treatment": 0.0971, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0104, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0273, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 18358091, "control": 9009281, "name": "Revenue", "total": 18182381, "treatment": 9173100, "type": "currency"}, {"adjusted": null, "control": 45.0, "name": "AOV", "total": 45.02, "treatment": 45.03, "type": "currency"}, {"adjusted": 351192, "control": null, "name": "Uplift", "total": null, "treatment": 175482, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0189, "actualQuantityUplift": 3769.19, "adjustedQuantityUplift": 7543.26, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 2.1733135736046108e-10}}]