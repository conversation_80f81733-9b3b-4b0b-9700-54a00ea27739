[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-10-25", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "fableticscom", "startDate": "2024-10-21"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 216209, "name": "Taggstar Experiment Sessions", "total": 432242, "treatment": 216033, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1421934, "name": "Taggstar Experiment Impressions", "total": 2836595, "treatment": 1414661, "type": "long"}, {"adjusted": null, "control": 0.5013, "name": "Experiment split", "total": null, "treatment": 0.4987, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1372152, "name": "Taggstar Message Impressions", "total": 2737094, "treatment": 1364942, "type": "long"}, {"adjusted": null, "control": 0.965, "name": "Taggstar coverage", "total": null, "treatment": 0.9649, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 16329, "control": 8286, "name": "Number of orders", "total": 16447, "treatment": 8161, "type": "long"}, {"adjusted": -237, "control": null, "name": "Uplift", "total": null, "treatment": -118, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0378, "control": 0.0383, "name": "Conversion rate", "total": 0.0381, "treatment": 0.0378, "type": "percentage"}, {"adjusted": -0.0143, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0143, "type": "percentage"}, {"adjusted": null, "control": 0.0375, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0745, "treatment": 0.037, "type": "percentage"}, {"adjusted": null, "control": 0.0391, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0777, "treatment": 0.0386, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0552, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0284, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1683755, "control": 857877, "name": "Revenue", "total": 1699411, "treatment": 841535, "type": "currency"}, {"adjusted": null, "control": 103.53, "name": "AOV", "total": 103.33, "treatment": 103.12, "type": "currency"}, {"adjusted": -31300, "control": null, "name": "Uplift", "total": null, "treatment": -15644, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0143, "actualQuantityUplift": -118.25, "adjustedQuantityUplift": -236.61, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8265303127426145}}]