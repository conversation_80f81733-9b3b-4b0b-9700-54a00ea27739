[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2023-11-15", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "fableticscom", "startDate": "2023-11-01"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1584426, "name": "Taggstar Experiment Sessions", "total": 3165508, "treatment": 1581082, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 17545990, "name": "Taggstar Experiment Impressions", "total": 34948336, "treatment": 17402346, "type": "long"}, {"adjusted": null, "control": 0.5021, "name": "Experiment split", "total": null, "treatment": 0.4979, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 16781058, "treatment": 16781058, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9643, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 127929, "control": 62254, "name": "Number of orders", "total": 126151, "treatment": 63897, "type": "long"}, {"adjusted": 3553, "control": null, "name": "Uplift", "total": null, "treatment": 1774, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0404, "control": 0.0393, "name": "Conversion rate", "total": 0.0399, "treatment": 0.0404, "type": "percentage"}, {"adjusted": 0.0286, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0286, "type": "percentage"}, {"adjusted": null, "control": 0.039, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0791, "treatment": 0.0401, "type": "percentage"}, {"adjusted": null, "control": 0.0396, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0803, "treatment": 0.0407, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.013, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0444, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 12254405, "control": 5999954, "name": "Revenue", "total": 12120684, "treatment": 6120730, "type": "currency"}, {"adjusted": null, "control": 96.38, "name": "AOV", "total": 96.08, "treatment": 95.79, "type": "currency"}, {"adjusted": 267159, "control": null, "name": "Uplift", "total": null, "treatment": 133439, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0286, "actualQuantityUplift": 1774.39, "adjustedQuantityUplift": 3552.53, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.6649909365545226e-07}}]