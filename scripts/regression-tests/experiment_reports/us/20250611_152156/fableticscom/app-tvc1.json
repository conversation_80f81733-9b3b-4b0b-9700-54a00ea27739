[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-10-29", "experimentId": "app-tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "fableticscom", "startDate": "2024-04-30"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1725422, "name": "Taggstar Experiment Sessions", "total": 3448809, "treatment": 1723387, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 14349608, "name": "Taggstar Experiment Impressions", "total": 28696738, "treatment": 14347130, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 2083655, "name": "Taggstar Message Impressions", "total": 11049682, "treatment": 8966027, "type": "long"}, {"adjusted": null, "control": 0.1452, "name": "Taggstar coverage", "total": null, "treatment": 0.6249, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 130427, "control": 62796, "name": "Number of orders", "total": 127971, "treatment": 65175, "type": "long"}, {"adjusted": 4909, "control": null, "name": "Uplift", "total": null, "treatment": 2453, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0378, "control": 0.0364, "name": "Conversion rate", "total": 0.0371, "treatment": 0.0378, "type": "percentage"}, {"adjusted": 0.0391, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0391, "type": "percentage"}, {"adjusted": null, "control": 0.0361, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0736, "treatment": 0.0375, "type": "percentage"}, {"adjusted": null, "control": 0.0367, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0748, "treatment": 0.0381, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0234, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.055, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 14579540, "control": 7028086, "name": "Revenue", "total": 14313554, "treatment": 7285468, "type": "currency"}, {"adjusted": null, "control": 111.92, "name": "AOV", "total": 111.85, "treatment": 111.78, "type": "currency"}, {"adjusted": 531657, "control": null, "name": "Uplift", "total": null, "treatment": 265671, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0391, "actualQuantityUplift": 2453.06, "adjustedQuantityUplift": 4909.02, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.3523589275845564e-12}}]