[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-12-06", "experimentId": "tvt3-v2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "fableticscom", "startDate": "2024-10-30"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5191463, "name": "Taggstar Experiment Sessions", "total": 10378987, "treatment": 5187524, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 41460675, "name": "Taggstar Experiment Impressions", "total": 82919719, "treatment": 41459044, "type": "long"}, {"adjusted": null, "control": 0.5, "name": "Experiment split", "total": null, "treatment": 0.5, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 40229646, "name": "Taggstar Message Impressions", "total": 80457608, "treatment": 40227962, "type": "long"}, {"adjusted": null, "control": 0.9703, "name": "Taggstar coverage", "total": null, "treatment": 0.9703, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 521272, "control": 260946, "name": "Number of orders", "total": 521483, "treatment": 260537, "type": "long"}, {"adjusted": -422, "control": null, "name": "Uplift", "total": null, "treatment": -211, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0502, "control": 0.0503, "name": "Conversion rate", "total": 0.0502, "treatment": 0.0502, "type": "percentage"}, {"adjusted": -0.0008, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0008, "type": "percentage"}, {"adjusted": null, "control": 0.0501, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.1001, "treatment": 0.05, "type": "percentage"}, {"adjusted": null, "control": 0.0505, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.1009, "treatment": 0.0504, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0083, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0067, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 53703398, "control": 26782971, "name": "Revenue", "total": 53624479, "treatment": 26841508, "type": "currency"}, {"adjusted": null, "control": 102.64, "name": "AOV", "total": 102.83, "treatment": 103.02, "type": "currency"}, {"adjusted": 157777, "control": null, "name": "Uplift", "total": null, "treatment": 78859, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0008, "actualQuantityUplift": -211.01, "adjustedQuantityUplift": -422.18, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.6178896132170443}}]