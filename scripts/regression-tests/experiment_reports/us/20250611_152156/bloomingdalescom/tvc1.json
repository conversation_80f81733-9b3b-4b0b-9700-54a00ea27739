[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-09-20", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "bloomingdalescom", "startDate": "2024-09-06"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 5257220, "name": "Taggstar Experiment Sessions", "total": 10510012, "treatment": 5252792, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 14887580, "name": "Taggstar Experiment Impressions", "total": 29808314, "treatment": 14920734, "type": "long"}, {"adjusted": null, "control": 0.4994, "name": "Experiment split", "total": null, "treatment": 0.5006, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 9982913, "treatment": 9982913, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.6691, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 174792, "control": 85697, "name": "Number of orders", "total": 173056, "treatment": 87359, "type": "long"}, {"adjusted": 3470, "control": null, "name": "Uplift", "total": null, "treatment": 1734, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0166, "control": 0.0163, "name": "Conversion rate", "total": 0.0165, "treatment": 0.0166, "type": "percentage"}, {"adjusted": 0.0203, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0203, "type": "percentage"}, {"adjusted": null, "control": 0.0162, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0327, "treatment": 0.0165, "type": "percentage"}, {"adjusted": null, "control": 0.0164, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0331, "treatment": 0.0167, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0069, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0338, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 61271892, "control": 30318135, "name": "Revenue", "total": 60941173, "treatment": 30623039, "type": "currency"}, {"adjusted": null, "control": 353.78, "name": "AOV", "total": 352.15, "treatment": 350.54, "type": "currency"}, {"adjusted": 661159, "control": null, "name": "Uplift", "total": null, "treatment": 330440, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0203, "actualQuantityUplift": 1734.18, "adjustedQuantityUplift": 3469.82, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.3042009210462754e-05}}]