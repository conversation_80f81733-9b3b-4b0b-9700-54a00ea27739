[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-05-20", "experimentId": "tvt3", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "bloomingdalescom", "startDate": "2025-05-12"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 3170228, "name": "Taggstar Experiment Sessions", "total": 6334053, "treatment": 3163825, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 8807607, "name": "Taggstar Experiment Impressions", "total": 17609234, "treatment": 8801627, "type": "long"}, {"adjusted": null, "control": 0.5002, "name": "Experiment split", "total": null, "treatment": 0.4998, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 6816457, "name": "Taggstar Message Impressions", "total": 13655872, "treatment": 6839415, "type": "long"}, {"adjusted": null, "control": 0.7739, "name": "Taggstar coverage", "total": null, "treatment": 0.7771, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 98179, "control": 48617, "name": "Number of orders", "total": 97657, "treatment": 49040, "type": "long"}, {"adjusted": 1043, "control": null, "name": "Uplift", "total": null, "treatment": 521, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0155, "control": 0.0153, "name": "Conversion rate", "total": 0.0154, "treatment": 0.0155, "type": "percentage"}, {"adjusted": 0.0107, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0107, "type": "percentage"}, {"adjusted": null, "control": 0.0152, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0306, "treatment": 0.0154, "type": "percentage"}, {"adjusted": null, "control": 0.0155, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0311, "treatment": 0.0156, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0069, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0287, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 34833501, "control": 17062895, "name": "Revenue", "total": 34462040, "treatment": 17399144, "type": "currency"}, {"adjusted": null, "control": 350.97, "name": "AOV", "total": 352.89, "treatment": 354.79, "type": "currency"}, {"adjusted": 742173, "control": null, "name": "Uplift", "total": null, "treatment": 370712, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0107, "actualQuantityUplift": 521.19, "adjustedQuantityUplift": 1043.44, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.04623493200839368}}]