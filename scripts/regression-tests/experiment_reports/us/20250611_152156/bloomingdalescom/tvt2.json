[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-05-06", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "bloomingdalescom", "startDate": "2025-04-18"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 9251611, "name": "Taggstar Experiment Sessions", "total": 18499451, "treatment": 9247840, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 22535711, "name": "Taggstar Experiment Impressions", "total": 45061881, "treatment": 22526170, "type": "long"}, {"adjusted": null, "control": 0.5001, "name": "Experiment split", "total": null, "treatment": 0.4999, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 17823585, "name": "Taggstar Message Impressions", "total": 38803559, "treatment": 20979974, "type": "long"}, {"adjusted": null, "control": 0.7909, "name": "Taggstar coverage", "total": null, "treatment": 0.9314, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 248699, "control": 124960, "name": "Number of orders", "total": 249284, "treatment": 124324, "type": "long"}, {"adjusted": -1170, "control": null, "name": "Uplift", "total": null, "treatment": -585, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0134, "control": 0.0135, "name": "Conversion rate", "total": 0.0135, "treatment": 0.0134, "type": "percentage"}, {"adjusted": -0.0047, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0047, "type": "percentage"}, {"adjusted": null, "control": 0.0134, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0268, "treatment": 0.0134, "type": "percentage"}, {"adjusted": null, "control": 0.0136, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0271, "treatment": 0.0135, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0156, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0064, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 87680440, "control": 44000961, "name": "Revenue", "total": 87832244, "treatment": 43831284, "type": "currency"}, {"adjusted": null, "control": 352.12, "name": "AOV", "total": 352.34, "treatment": 352.56, "type": "currency"}, {"adjusted": -303547, "control": null, "name": "Uplift", "total": null, "treatment": -151742, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0047, "actualQuantityUplift": -585.07, "adjustedQuantityUplift": -1170.37, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8810053985122086}}]