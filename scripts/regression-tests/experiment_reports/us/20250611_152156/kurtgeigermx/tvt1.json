[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-05-20", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "kurtgeigermx", "startDate": "2025-02-17"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 560829, "name": "Taggstar Experiment Sessions", "total": 1125097, "treatment": 564268, "type": "long"}, {"adjusted": null, "control": 0.4985, "name": "Experiment split", "total": null, "treatment": 0.5015, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2165195, "name": "Taggstar Experiment Impressions", "total": 4354191, "treatment": 2188996, "type": "long"}, {"adjusted": null, "control": 0.4973, "name": "Experiment split", "total": null, "treatment": 0.5027, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 1524225, "name": "Taggstar Message Impressions", "total": 3071940, "treatment": 1547715, "type": "long"}, {"adjusted": null, "control": 0.704, "name": "Taggstar coverage", "total": null, "treatment": 0.707, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 5055, "control": 2354, "name": "Number of orders", "total": 4889, "treatment": 2535, "type": "long"}, {"adjusted": 332, "control": null, "name": "Uplift", "total": null, "treatment": 167, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0045, "control": 0.0042, "name": "Conversion rate", "total": 0.0043, "treatment": 0.0045, "type": "percentage"}, {"adjusted": 0.0703, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0703, "type": "percentage"}, {"adjusted": null, "control": 0.004, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0083, "treatment": 0.0043, "type": "percentage"}, {"adjusted": null, "control": 0.0044, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.009, "treatment": 0.0047, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0111, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.1586, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1648790, "control": 759770, "name": "Revenue", "total": 1586685, "treatment": 826915, "type": "currency"}, {"adjusted": null, "control": 322.76, "name": "AOV", "total": 324.54, "treatment": 326.2, "type": "currency"}, {"adjusted": 124591, "control": null, "name": "Uplift", "total": null, "treatment": 62486, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0703, "actualQuantityUplift": 166.57, "adjustedQuantityUplift": 332.12, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.008648381478816441}}]