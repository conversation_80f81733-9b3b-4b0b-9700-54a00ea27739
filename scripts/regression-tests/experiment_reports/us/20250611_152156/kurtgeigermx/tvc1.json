[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-01-10", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "kurtgeigermx", "startDate": "2024-12-04"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 287704, "name": "Taggstar Experiment Sessions", "total": 578000, "treatment": 290296, "type": "long"}, {"adjusted": null, "control": 0.4978, "name": "Experiment split", "total": null, "treatment": 0.5022, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1182835, "name": "Taggstar Experiment Impressions", "total": 2384790, "treatment": 1201955, "type": "long"}, {"adjusted": null, "control": 0.496, "name": "Experiment split", "total": null, "treatment": 0.504, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 1007257, "treatment": 1007257, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.838, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 4291, "control": 1924, "name": "Number of orders", "total": 4079, "treatment": 2155, "type": "long"}, {"adjusted": 425, "control": null, "name": "Uplift", "total": null, "treatment": 214, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0074, "control": 0.0067, "name": "Conversion rate", "total": 0.0071, "treatment": 0.0074, "type": "percentage"}, {"adjusted": 0.1101, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.1101, "type": "percentage"}, {"adjusted": null, "control": 0.0064, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0135, "treatment": 0.0071, "type": "percentage"}, {"adjusted": null, "control": 0.007, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0147, "treatment": 0.0077, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.018, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.2107, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 1268778, "control": 566522, "name": "Revenue", "total": 1203756, "treatment": 637234, "type": "currency"}, {"adjusted": null, "control": 294.45, "name": "AOV", "total": 295.11, "treatment": 295.7, "type": "currency"}, {"adjusted": 130630, "control": null, "name": "Uplift", "total": null, "treatment": 65608, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.1101, "actualQuantityUplift": 213.67, "adjustedQuantityUplift": 425.42, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.00041418362723456435}}]