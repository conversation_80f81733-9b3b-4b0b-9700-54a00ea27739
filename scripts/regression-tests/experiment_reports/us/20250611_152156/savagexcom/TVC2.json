[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2025-01-28", "experimentId": "TVC2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "savagexcom", "startDate": "2025-01-13"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 821781, "name": "Taggstar Experiment Sessions", "total": 1642423, "treatment": 820642, "type": "long"}, {"adjusted": null, "control": 0.5003, "name": "Experiment split", "total": null, "treatment": 0.4997, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 4827873, "name": "Taggstar Experiment Impressions", "total": 9644784, "treatment": 4816911, "type": "long"}, {"adjusted": null, "control": 0.5006, "name": "Experiment split", "total": null, "treatment": 0.4994, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 4405531, "treatment": 4405531, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.9146, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 41389, "control": 20190, "name": "Number of orders", "total": 40870, "treatment": 20680, "type": "long"}, {"adjusted": 1037, "control": null, "name": "Uplift", "total": null, "treatment": 518, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0252, "control": 0.0246, "name": "Conversion rate", "total": 0.0249, "treatment": 0.0252, "type": "percentage"}, {"adjusted": 0.0257, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0257, "type": "percentage"}, {"adjusted": null, "control": 0.0242, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0491, "treatment": 0.0249, "type": "percentage"}, {"adjusted": null, "control": 0.0249, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0504, "treatment": 0.0255, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0017, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0539, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 2082703, "control": 1019664, "name": "Revenue", "total": 2060293, "treatment": 1040629, "type": "currency"}, {"adjusted": null, "control": 50.5, "name": "AOV", "total": 50.41, "treatment": 50.32, "type": "currency"}, {"adjusted": 44788, "control": null, "name": "Uplift", "total": null, "treatment": 22378, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0257, "actualQuantityUplift": 517.98, "adjustedQuantityUplift": 1036.69, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 0.0047093073608265}}]