[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-12-05", "experimentId": "tvt2", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "savagexcom", "startDate": "2024-11-07"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 2035538, "name": "Taggstar Experiment Sessions", "total": 4072351, "treatment": 2036813, "type": "long"}, {"adjusted": null, "control": 0.4998, "name": "Experiment split", "total": null, "treatment": 0.5002, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 12871739, "name": "Taggstar Experiment Impressions", "total": 25718610, "treatment": 12846871, "type": "long"}, {"adjusted": null, "control": 0.5005, "name": "Experiment split", "total": null, "treatment": 0.4995, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 11999470, "name": "Taggstar Message Impressions", "total": 24471076, "treatment": 12471606, "type": "long"}, {"adjusted": null, "control": 0.9322, "name": "Taggstar coverage", "total": null, "treatment": 0.9708, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 133332, "control": 66995, "name": "Number of orders", "total": 133682, "treatment": 66687, "type": "long"}, {"adjusted": -700, "control": null, "name": "Uplift", "total": null, "treatment": -350, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0327, "control": 0.0329, "name": "Conversion rate", "total": 0.0328, "treatment": 0.0327, "type": "percentage"}, {"adjusted": -0.0052, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0052, "type": "percentage"}, {"adjusted": null, "control": 0.0327, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0652, "treatment": 0.0325, "type": "percentage"}, {"adjusted": null, "control": 0.0332, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0661, "treatment": 0.033, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0199, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0097, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 7371159, "control": 3740268, "name": "Revenue", "total": 7427001, "treatment": 3686733, "type": "currency"}, {"adjusted": null, "control": 55.83, "name": "AOV", "total": 55.56, "treatment": 55.28, "type": "currency"}, {"adjusted": -111719, "control": null, "name": "Uplift", "total": null, "treatment": -55877, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0052, "actualQuantityUplift": -349.96, "adjustedQuantityUplift": -699.71, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.8347151294581866}}]