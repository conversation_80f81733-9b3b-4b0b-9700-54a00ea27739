[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-06-05", "experimentId": "tvt1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "savagexcom", "startDate": "2024-05-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1057326, "name": "Taggstar Experiment Sessions", "total": 2112920, "treatment": 1055594, "type": "long"}, {"adjusted": null, "control": 0.5004, "name": "Experiment split", "total": null, "treatment": 0.4996, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 8029127, "name": "Taggstar Experiment Impressions", "total": 15984744, "treatment": 7955617, "type": "long"}, {"adjusted": null, "control": 0.5023, "name": "Experiment split", "total": null, "treatment": 0.4977, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 7266490, "name": "Taggstar Message Impressions", "total": 14143562, "treatment": 6877072, "type": "long"}, {"adjusted": null, "control": 0.905, "name": "Taggstar coverage", "total": null, "treatment": 0.8644, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 70364, "control": 35463, "name": "Number of orders", "total": 70616, "treatment": 35153, "type": "long"}, {"adjusted": -504, "control": null, "name": "Uplift", "total": null, "treatment": -252, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0333, "control": 0.0335, "name": "Conversion rate", "total": 0.0334, "treatment": 0.0333, "type": "percentage"}, {"adjusted": -0.0071, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": -0.0071, "type": "percentage"}, {"adjusted": null, "control": 0.0332, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0662, "treatment": 0.033, "type": "percentage"}, {"adjusted": null, "control": 0.0339, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0675, "treatment": 0.0336, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": -0.0273, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0135, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 3540470, "control": 1800345, "name": "Revenue", "total": 3569129, "treatment": 1768784, "type": "currency"}, {"adjusted": null, "control": 50.77, "name": "AOV", "total": 50.54, "treatment": 50.32, "type": "currency"}, {"adjusted": -57271, "control": null, "name": "Uplift", "total": null, "treatment": -28612, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "NO", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": -0.0071, "actualQuantityUplift": -251.91, "adjustedQuantityUplift": -504.23, "confidenceLevel": 0.95, "statisticalSignificance": false, "statisticalSignificancePValue": 0.832728806436967}}]