[{"category": "Overall", "metaData": {"categoryEnabled": "false", "conversionDim1Enabled": "false", "devices": "desktop,mobile,tablet,application", "endDate": "2024-03-20", "experimentId": "tvc1", "experimentState": "finished", "isSeries": "false", "metric": "sessions", "orderType": "conversions", "siteKey": "savagexcom", "startDate": "2024-02-09"}, "reportTable": {"sections": [{"name": "Sessions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 1801399, "name": "Taggstar Experiment Sessions", "total": 3606521, "treatment": 1805122, "type": "long"}, {"adjusted": null, "control": 0.4995, "name": "Experiment split", "total": null, "treatment": 0.5005, "type": "percentage"}]}]}, {"name": "Impressions", "subSections": [{"name": null, "rows": [{"adjusted": null, "control": 12151980, "name": "Taggstar Experiment Impressions", "total": 24330136, "treatment": 12178156, "type": "long"}, {"adjusted": null, "control": 0.4995, "name": "Experiment split", "total": null, "treatment": 0.5005, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": null, "control": 0, "name": "Taggstar Message Impressions", "total": 11422689, "treatment": 11422689, "type": "long"}, {"adjusted": null, "control": 0.0, "name": "Taggstar coverage", "total": null, "treatment": 0.938, "type": "percentage"}]}]}, {"name": "Total", "subSections": [{"name": null, "rows": [{"adjusted": 121397, "control": 58484, "name": "Number of orders", "total": 119245, "treatment": 60761, "type": "long"}, {"adjusted": 4308, "control": null, "name": "Uplift", "total": null, "treatment": 2156, "type": "long"}]}, {"name": null, "rows": [{"adjusted": 0.0337, "control": 0.0325, "name": "Conversion rate", "total": 0.0331, "treatment": 0.0337, "type": "percentage"}, {"adjusted": 0.0368, "control": null, "name": "Conversion rate uplift", "total": null, "treatment": 0.0368, "type": "percentage"}, {"adjusted": null, "control": 0.0322, "name": "Conversion Rate Lower Bound (95% CI)", "total": 0.0656, "treatment": 0.0334, "type": "percentage"}, {"adjusted": null, "control": 0.0327, "name": "Conversion Rate Upper Bound (95% CI)", "total": 0.0666, "treatment": 0.0339, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift lower bound (95% CI)", "total": null, "treatment": 0.0206, "type": "percentage"}, {"adjusted": null, "control": null, "name": "Conversion rate uplift upper bound (95% CI)", "total": null, "treatment": 0.0533, "type": "percentage"}]}, {"name": null, "rows": [{"adjusted": 6050562, "control": 2933362, "name": "Revenue", "total": 5961766, "treatment": 3028404, "type": "currency"}, {"adjusted": null, "control": 50.16, "name": "AOV", "total": 50.0, "treatment": 49.84, "type": "currency"}, {"adjusted": 177775, "control": null, "name": "Uplift", "total": null, "treatment": 88979, "type": "currency"}]}, {"name": null, "rows": [{"adjusted": null, "control": null, "name": "Statistical Significance", "total": null, "treatment": "YES", "type": "string"}]}]}]}, "uplift": {"actualPercentageUplift": 0.0368, "actualQuantityUplift": 2156.13, "adjustedQuantityUplift": 4307.81, "confidenceLevel": 0.95, "statisticalSignificance": true, "statisticalSignificancePValue": 1.1242073757249301e-10}}]