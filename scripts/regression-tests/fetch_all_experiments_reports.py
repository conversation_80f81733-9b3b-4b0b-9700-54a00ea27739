import json
import pathlib
import requests
from dataclasses import dataclass
from datetime import date, datetime
from enum import Enum
from typing import List, Optional, Dict
from urllib.parse import urljoin, urlencode


class Region(str, Enum):
    EU = "eu"
    US = "us"
    EU_LOCAL = "eu-local"
    US_LOCAL = "us-local"


@dataclass
class RegionConfig:
    config_host: str
    reporting_host: str
    output_dir: str

    @classmethod
    def for_region(cls, region: Region) -> 'RegionConfig':
        configs = {
            Region.EU: cls(
                config_host='http://prdconfig01.taggstar.net:8080',
                reporting_host='http://prdreporting01.taggstar.net:8000',
                output_dir='experiment_reports/eu'
            ),
            Region.US: cls(
                config_host='http://*************:8080',
                reporting_host='http://*************:8000',
                output_dir='experiment_reports/us'
            ),
            Region.EU_LOCAL: cls(
                config_host='http://prdconfig01.taggstar.net:8080',
                reporting_host='http://localhost:8000',
                output_dir='experiment_reports/eu-local'
            ),
            Region.US_LOCAL: cls(
                config_host='http://*************:8080',
                reporting_host='http://localhost:8000',
                output_dir='experiment_reports/us-local'
            )
        }
        return configs[region]


@dataclass
class ServiceConfig:
    region: Region
    all_configs_endpoint: str = '/api/v2/sites/configs'
    experiment_report_endpoint: str = '/api/v3/site/{site_key}/experiment/report'
    experiment_series_endpoint: str = '/api/v3/site/{site_key}/experiment/report/series'

    def __post_init__(self):
        self.region_config = RegionConfig.for_region(self.region)

    @property
    def config_host(self) -> str:
        return self.region_config.config_host

    @property
    def reporting_host(self) -> str:
        return self.region_config.reporting_host

    @property
    def output_dir(self) -> str:
        return self.region_config.output_dir


@dataclass
class Experiment:
    id: str
    start_date: str
    end_date: str
    state: str

    @classmethod
    def from_dict(cls, data: Dict) -> 'Experiment':
        start_date = data['startDate'].split('T')[0] if data.get('startDate') else None
        
        raw_end_date = data.get('endDate')
        state = data['state']
        
        if state == 'running' and (not raw_end_date or raw_end_date == 'null'):
            end_date = date.today().strftime('%Y-%m-%d')
        else:
            end_date = raw_end_date.split('T')[0] if raw_end_date else date.today().strftime('%Y-%m-%d')
        
        return cls(
            id=data['id'],
            start_date=start_date,
            end_date=end_date,
            state=state
        )


@dataclass
class ReportMetrics:
    site_key: str
    experiment_id: str
    report_type: str  # 'report' or 'series'
    url: str
    start_time: datetime
    end_time: datetime
    status: str  # 'success' or 'error'
    error_message: Optional[str] = None
    
    @property
    def duration_ms(self) -> float:
        return (self.end_time - self.start_time).total_seconds() * 1000
    
    def to_dict(self) -> Dict:
        return {
            'site_key': self.site_key,
            'experiment_id': self.experiment_id,
            'report_type': self.report_type,
            'url': self.url,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'duration_ms': self.duration_ms,
            'status': self.status,
            'error_message': self.error_message
        }


@dataclass
class Manifest:
    timestamp: datetime
    region: Region
    metrics: List[ReportMetrics] = None
    
    def __post_init__(self):
        self.metrics = self.metrics or []
    
    def add_metric(self, metric: ReportMetrics):
        self.metrics.append(metric)
    
    def to_dict(self) -> Dict:
        return {
            'timestamp': self.timestamp.isoformat(),
            'region': self.region.value,
            'total_reports': len(self.metrics),
            'successful_reports': len([m for m in self.metrics if m.status == 'success']),
            'failed_reports': len([m for m in self.metrics if m.status == 'error']),
            'average_duration_ms': sum(m.duration_ms for m in self.metrics) / len(self.metrics) if self.metrics else 0,
            'metrics': [m.to_dict() for m in self.metrics]
        }
    
    def save(self, output_dir: pathlib.Path):
        manifest_path = output_dir / 'manifest.json'
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, sort_keys=True)


class ExperimentReporter:
    def __init__(self, region: Region = Region.EU, refresh_cache: bool = False):
        self.config = ServiceConfig(region=region)
        self.refresh_cache = refresh_cache
        self.session = requests.Session()
        self.setup_output_directory()
        self.manifest = Manifest(timestamp=datetime.now(), region=region)
        print(f"Initialized reporter for region: {region.value}")
    
    def setup_output_directory(self):
        """Create output directory with timestamp."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.output_dir = pathlib.Path(self.config.output_dir) / timestamp
        self.output_dir.mkdir(parents=True, exist_ok=True)
        print(f"Saving reports to: {self.output_dir}")

    def _get_reporting_params(self, experiment: Experiment, timezone: str) -> Dict:
        """Generate parameters for the reporting endpoint."""
        return {
            'deviceType': ['desktop', 'mobile', 'tablet', 'application'],
            'experimentId': experiment.id,
            'refreshCache': 'true' if self.refresh_cache else 'false'
        }

    def get_site_configs(self) -> List[Dict]:
        """Fetch site configurations from the config service."""
        url = urljoin(self.config.config_host, self.config.all_configs_endpoint)
        print(f"Fetching configs from: {url}")
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()['siteConfigsList']

    def _track_request(self, site_key: str, experiment_id: str, report_type: str, url: str) -> ReportMetrics:
        """Create and return a new ReportMetrics instance for tracking a request."""
        return ReportMetrics(
            site_key=site_key,
            experiment_id=experiment_id,
            report_type=report_type,
            url=url,
            start_time=datetime.now(),
            end_time=datetime.now(),  # Will be updated after request completes
            status='success'  # Will be updated if error occurs
        )
    
    def get_experiment_series(self, site_key: str, experiment: Experiment, timezone: str) -> Dict:
        """Fetch experiment series data from the reporting service with metrics tracking."""
        url = urljoin(
            self.config.reporting_host,
            self.config.experiment_series_endpoint.format(site_key=site_key)
        )
        params = self._get_reporting_params(experiment, timezone)
        params['interval'] = 'day'
        
        query_params = urlencode(params, doseq=True)
        full_url = f"{url}?{query_params}"
        
        metrics = self._track_request(site_key, experiment.id, 'series', full_url)
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            metrics.end_time = datetime.now()
            self.manifest.add_metric(metrics)
            return response.json()
        except Exception as e:
            metrics.status = 'error'
            metrics.error_message = str(e)
            metrics.end_time = datetime.now()
            self.manifest.add_metric(metrics)
            raise

    def get_experiment_report(self, site_key: str, experiment: Experiment, timezone: str) -> Dict:
        """Fetch experiment report from the reporting service with metrics tracking."""
        url = urljoin(
            self.config.reporting_host,
            self.config.experiment_report_endpoint.format(site_key=site_key)
        )
        params = self._get_reporting_params(experiment, timezone)
        
        query_params = urlencode(params, doseq=True)
        full_url = f"{url}?{query_params}"
        
        metrics = self._track_request(site_key, experiment.id, 'report', full_url)
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            metrics.end_time = datetime.now()
            self.manifest.add_metric(metrics)
            return response.json()
        except Exception as e:
            metrics.status = 'error'
            metrics.error_message = str(e)
            metrics.end_time = datetime.now()
            self.manifest.add_metric(metrics)
            raise

    def process_site_experiments(self):
        """Process all experiments for all sites and save manifest."""
        try:
            sites_configs = self.get_site_configs()
            total_sites = len(sites_configs)
            
            for site_idx, site_config in enumerate(sites_configs, 1):
                self.process_single_site(site_idx, total_sites, site_config)
                
        except requests.RequestException as e:
            print(f"Error fetching site configs: {e}")
        finally:
            # Save manifest even if there were errors
            self.manifest.save(self.output_dir)
            print(f"Saved manifest to: {self.output_dir / 'manifest.json'}")

    def process_single_site(self, site_idx: int, total_sites: int, site_config: Dict):
        """Process experiments for a single site."""
        config = site_config['configs']['siteConfig']['config']

        if config.get('siteKeyEnabled', False) is False:
            return

        site_key = site_config['siteKey']
        timezone = config['defaultTimeZone']

        print(
            f"({site_idx}/{total_sites}) Fetching experiment reports for site_key: {site_key}"
        )

        experiments = config.get('experiments', [])
        finished_experiments = [exp for exp in experiments if exp['state'] == 'finished']
        total_experiments = len(finished_experiments)
        
        for exp_idx, exp_data in enumerate(finished_experiments, 1):
            try:
                experiment = Experiment.from_dict(exp_data)
                self.process_single_experiment(exp_idx, total_experiments, site_key, experiment, timezone)
            except KeyError as e:
                print(f"Error processing experiment data for site {site_key}: {e} - {exp_data}")
            except Exception as e:
                print(f"Unexpected error processing experiment for site {site_key}: {e} - {exp_data}")

    def process_single_experiment(self, exp_idx: int, total_experiments: int,
                                  site_key: str, experiment: Experiment, timezone: str):
        """Process a single experiment and fetch its report."""
        print(f"    ({exp_idx}/{total_experiments}) experiment_id: {experiment.id}, "
              f"state: {experiment.state} "
        )
        
        try:
            report_data = self.get_experiment_report(site_key, experiment, timezone)
            series_data = self.get_experiment_series(site_key, experiment, timezone)
            self.save_report(site_key, experiment.id, report_data)
            self.save_report(site_key, f"{experiment.id}_series", series_data)
            print(f"        Saved report for experiment: {experiment.id}")
        except requests.RequestException as e:
            print(f"Error fetching report for {experiment.id}: {e}")
        except ValueError as e:
            print(f"Error parsing report response for {experiment.id}: {e}")
        except IOError as e:
            print(f"Error saving report for {experiment.id}: {e}")

    def save_report(self, site_key: str, experiment_id: str, report_data: Dict):
        """Save report data to a JSON file."""
        file_path = self.output_dir / f"{site_key}" / f"{experiment_id}.json"
        file_path.parent.mkdir(parents=True, exist_ok=True)

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, sort_keys=True)

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Fetch experiment reports for a specific region')
    parser.add_argument(
        '--region',
        type=str,
        choices=[r.value for r in Region],
        default=Region.EU.value,
        help='Region to fetch reports for (eu, us, or local)'
    )

    parser.add_argument(
        '--refreshCache',
        action='store_true',
        help='Refresh the cache for the reports'
    )
    
    args = parser.parse_args()
    region = Region(args.region)
    refresh_cache = args.refreshCache or False
    
    reporter = ExperimentReporter(region=region, refresh_cache=refresh_cache)
    reporter.process_site_experiments()


if __name__ == "__main__":
    main()